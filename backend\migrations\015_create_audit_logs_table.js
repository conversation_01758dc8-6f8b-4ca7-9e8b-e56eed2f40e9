/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('audit_logs', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // User information
    table.uuid('user_id').nullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('SET NULL');
    table.string('user_email', 255).nullable();
    table.string('user_role', 50).nullable();
    
    // Action details
    table.string('action', 100).notNullable();
    table.string('resource_type', 100).nullable(); // user, portfolio, transaction, etc.
    table.uuid('resource_id').nullable();
    table.enum('severity', ['info', 'warning', 'error', 'critical']).defaultTo('info');
    
    // Request information
    table.string('method', 10).nullable(); // GET, POST, PUT, DELETE
    table.string('endpoint', 500).nullable();
    table.string('ip_address', 45).nullable();
    table.string('user_agent', 1000).nullable();
    table.json('request_headers').nullable();
    table.json('request_body').nullable();
    table.json('response_body').nullable();
    table.integer('status_code').nullable();
    
    // Changes tracking
    table.json('old_values').nullable();
    table.json('new_values').nullable();
    table.json('changes').nullable();
    
    // Context and metadata
    table.text('description').nullable();
    table.json('metadata').nullable();
    table.string('session_id', 255).nullable();
    table.string('request_id', 255).nullable();
    
    // Geolocation
    table.string('country', 2).nullable();
    table.string('city', 100).nullable();
    table.decimal('latitude', 10, 8).nullable();
    table.decimal('longitude', 11, 8).nullable();
    
    // Timestamps
    table.timestamp('created_at').defaultTo(knex.fn.now());
    
    // Indexes
    table.index(['user_id']);
    table.index(['action']);
    table.index(['resource_type']);
    table.index(['resource_id']);
    table.index(['severity']);
    table.index(['ip_address']);
    table.index(['created_at']);
    table.index(['user_id', 'created_at']);
    table.index(['action', 'created_at']);
    table.index(['resource_type', 'resource_id']);
    table.index(['session_id']);
    table.index(['request_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('audit_logs');
};
