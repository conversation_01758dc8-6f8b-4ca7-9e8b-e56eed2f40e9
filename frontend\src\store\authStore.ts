import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import toast from 'react-hot-toast';

// Services
import { authService } from '@/services/authService';

// Types
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: 'user' | 'admin' | 'premium';
  emailVerified: boolean;
  twoFactorEnabled: boolean;
  avatar?: string;
  createdAt: string;
  lastLogin?: string;
}

export interface AuthState {
  // State
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<boolean>;
  register: (userData: RegisterData) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  updateUser: (userData: Partial<User>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  
  // Password reset
  forgotPassword: (email: string) => Promise<boolean>;
  resetPassword: (token: string, password: string) => Promise<boolean>;
  
  // Email verification
  verifyEmail: (token: string) => Promise<boolean>;
  resendVerification: (email: string) => Promise<boolean>;
  
  // Two-factor authentication
  enableTwoFactor: () => Promise<{ secret: string; qrCode: string } | null>;
  verifyTwoFactor: (token: string) => Promise<boolean>;
  disableTwoFactor: (token: string) => Promise<boolean>;
}

export interface RegisterData {
  email: string;
  password: string;
  username: string;
  firstName: string;
  lastName: string;
}

export const useAuthStore = create<AuthState>()(
  persist(
    immer((set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Login action
      login: async (email: string, password: string) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const response = await authService.login(email, password);
          
          if (response.success && response.data) {
            set((state) => {
              state.user = response.data.user;
              state.token = response.data.token;
              state.isAuthenticated = true;
              state.isLoading = false;
              state.error = null;
            });

            toast.success('Login successful!');
            return true;
          } else {
            set((state) => {
              state.error = response.message || 'Login failed';
              state.isLoading = false;
            });
            
            toast.error(response.message || 'Login failed');
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          
          set((state) => {
            state.error = errorMessage;
            state.isLoading = false;
          });
          
          toast.error(errorMessage);
          return false;
        }
      },

      // Register action
      register: async (userData: RegisterData) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const response = await authService.register(userData);
          
          if (response.success) {
            set((state) => {
              state.isLoading = false;
              state.error = null;
            });

            toast.success('Registration successful! Please check your email to verify your account.');
            return true;
          } else {
            set((state) => {
              state.error = response.message || 'Registration failed';
              state.isLoading = false;
            });
            
            toast.error(response.message || 'Registration failed');
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          
          set((state) => {
            state.error = errorMessage;
            state.isLoading = false;
          });
          
          toast.error(errorMessage);
          return false;
        }
      },

      // Logout action
      logout: async () => {
        try {
          await authService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set((state) => {
            state.user = null;
            state.token = null;
            state.isAuthenticated = false;
            state.error = null;
          });
          
          toast.success('Logged out successfully');
        }
      },

      // Refresh token action
      refreshToken: async () => {
        const { token } = get();
        
        if (!token) {
          return false;
        }

        try {
          const response = await authService.refreshToken();
          
          if (response.success && response.data) {
            set((state) => {
              state.user = response.data.user;
              state.token = response.data.token;
              state.isAuthenticated = true;
            });
            
            return true;
          } else {
            // Token refresh failed, logout user
            get().logout();
            return false;
          }
        } catch (error) {
          console.error('Token refresh error:', error);
          get().logout();
          return false;
        }
      },

      // Update user action
      updateUser: (userData: Partial<User>) => {
        set((state) => {
          if (state.user) {
            state.user = { ...state.user, ...userData };
          }
        });
      },

      // Clear error action
      clearError: () => {
        set((state) => {
          state.error = null;
        });
      },

      // Set loading action
      setLoading: (loading: boolean) => {
        set((state) => {
          state.isLoading = loading;
        });
      },

      // Forgot password action
      forgotPassword: async (email: string) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const response = await authService.forgotPassword(email);
          
          set((state) => {
            state.isLoading = false;
          });

          if (response.success) {
            toast.success('Password reset email sent!');
            return true;
          } else {
            toast.error(response.message || 'Failed to send reset email');
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email';
          
          set((state) => {
            state.error = errorMessage;
            state.isLoading = false;
          });
          
          toast.error(errorMessage);
          return false;
        }
      },

      // Reset password action
      resetPassword: async (token: string, password: string) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const response = await authService.resetPassword(token, password);
          
          set((state) => {
            state.isLoading = false;
          });

          if (response.success) {
            toast.success('Password reset successful!');
            return true;
          } else {
            toast.error(response.message || 'Password reset failed');
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Password reset failed';
          
          set((state) => {
            state.error = errorMessage;
            state.isLoading = false;
          });
          
          toast.error(errorMessage);
          return false;
        }
      },

      // Verify email action
      verifyEmail: async (token: string) => {
        set((state) => {
          state.isLoading = true;
          state.error = null;
        });

        try {
          const response = await authService.verifyEmail(token);
          
          set((state) => {
            state.isLoading = false;
          });

          if (response.success) {
            // Update user's email verification status
            set((state) => {
              if (state.user) {
                state.user.emailVerified = true;
              }
            });
            
            toast.success('Email verified successfully!');
            return true;
          } else {
            toast.error(response.message || 'Email verification failed');
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Email verification failed';
          
          set((state) => {
            state.error = errorMessage;
            state.isLoading = false;
          });
          
          toast.error(errorMessage);
          return false;
        }
      },

      // Resend verification action
      resendVerification: async (email: string) => {
        try {
          const response = await authService.resendVerification(email);
          
          if (response.success) {
            toast.success('Verification email sent!');
            return true;
          } else {
            toast.error(response.message || 'Failed to send verification email');
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to send verification email';
          toast.error(errorMessage);
          return false;
        }
      },

      // Enable two-factor authentication
      enableTwoFactor: async () => {
        try {
          const response = await authService.enableTwoFactor();
          
          if (response.success && response.data) {
            return response.data;
          } else {
            toast.error(response.message || 'Failed to enable two-factor authentication');
            return null;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to enable two-factor authentication';
          toast.error(errorMessage);
          return null;
        }
      },

      // Verify two-factor authentication
      verifyTwoFactor: async (token: string) => {
        try {
          const response = await authService.verifyTwoFactor(token);
          
          if (response.success) {
            set((state) => {
              if (state.user) {
                state.user.twoFactorEnabled = true;
              }
            });
            
            toast.success('Two-factor authentication enabled!');
            return true;
          } else {
            toast.error(response.message || 'Two-factor verification failed');
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Two-factor verification failed';
          toast.error(errorMessage);
          return false;
        }
      },

      // Disable two-factor authentication
      disableTwoFactor: async (token: string) => {
        try {
          const response = await authService.disableTwoFactor(token);
          
          if (response.success) {
            set((state) => {
              if (state.user) {
                state.user.twoFactorEnabled = false;
              }
            });
            
            toast.success('Two-factor authentication disabled!');
            return true;
          } else {
            toast.error(response.message || 'Failed to disable two-factor authentication');
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to disable two-factor authentication';
          toast.error(errorMessage);
          return false;
        }
      },
    })),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
