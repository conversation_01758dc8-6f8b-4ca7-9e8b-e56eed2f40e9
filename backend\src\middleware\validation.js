const { body, query, param, validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');

// Common validation rules
const commonValidations = {
  // UUID validation
  uuid: (field) => param(field).isUUID().withMessage(`${field} must be a valid UUID`),
  
  // Email validation
  email: (field = 'email') => body(field)
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  // Password validation
  password: (field = 'password') => body(field)
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'),
  
  // Username validation
  username: (field = 'username') => body(field)
    .isLength({ min: 3, max: 30 })
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens'),
  
  // Name validation
  name: (field) => body(field)
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z\s'-]+$/)
    .trim()
    .withMessage(`${field} must be 1-100 characters and contain only letters, spaces, apostrophes, and hyphens`),
  
  // Cryptocurrency symbol validation
  symbol: (field = 'symbol') => body(field)
    .isLength({ min: 1, max: 20 })
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Symbol must be 1-20 uppercase letters and numbers only'),
  
  // Amount validation (for crypto amounts)
  amount: (field = 'amount') => body(field)
    .isFloat({ min: 0 })
    .withMessage(`${field} must be a positive number`),
  
  // Price validation
  price: (field = 'price') => body(field)
    .isFloat({ min: 0 })
    .withMessage(`${field} must be a positive number`),
  
  // Pagination validation
  pagination: () => [
    query('page')
      .optional()
      .isInt({ min: 1, max: 10000 })
      .withMessage('Page must be a positive integer between 1 and 10000'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('Limit must be a positive integer between 1 and 1000')
  ],
  
  // Date validation
  date: (field) => body(field)
    .optional()
    .isISO8601()
    .withMessage(`${field} must be a valid ISO 8601 date`),
  
  // URL validation
  url: (field) => body(field)
    .optional()
    .isURL({ protocols: ['http', 'https'] })
    .withMessage(`${field} must be a valid HTTP/HTTPS URL`),
  
  // JSON validation
  json: (field) => body(field)
    .optional()
    .custom((value) => {
      try {
        JSON.parse(value);
        return true;
      } catch (error) {
        throw new Error(`${field} must be valid JSON`);
      }
    }),
  
  // Array validation
  array: (field, options = {}) => body(field)
    .optional()
    .isArray(options)
    .withMessage(`${field} must be an array`),
  
  // Boolean validation
  boolean: (field) => body(field)
    .optional()
    .isBoolean()
    .withMessage(`${field} must be a boolean value`),
  
  // Enum validation
  enum: (field, values) => body(field)
    .optional()
    .isIn(values)
    .withMessage(`${field} must be one of: ${values.join(', ')}`),
  
  // Text validation with length limits
  text: (field, minLength = 1, maxLength = 1000) => body(field)
    .optional()
    .isLength({ min: minLength, max: maxLength })
    .trim()
    .withMessage(`${field} must be between ${minLength} and ${maxLength} characters`),
  
  // Phone number validation
  phone: (field = 'phone') => body(field)
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  
  // Country code validation
  countryCode: (field = 'country') => body(field)
    .optional()
    .isLength({ min: 2, max: 2 })
    .isAlpha()
    .toUpperCase()
    .withMessage('Country must be a valid 2-letter country code'),
  
  // Language code validation
  languageCode: (field = 'language') => body(field)
    .optional()
    .isLength({ min: 2, max: 5 })
    .matches(/^[a-z]{2}(-[A-Z]{2})?$/)
    .withMessage('Language must be a valid language code (e.g., en, en-US)'),
  
  // Timezone validation
  timezone: (field = 'timezone') => body(field)
    .optional()
    .custom((value) => {
      try {
        Intl.DateTimeFormat(undefined, { timeZone: value });
        return true;
      } catch (error) {
        throw new Error('Invalid timezone');
      }
    }),
  
  // IP address validation
  ip: (field = 'ip') => body(field)
    .optional()
    .isIP()
    .withMessage('Must be a valid IP address'),
  
  // Hex color validation
  hexColor: (field = 'color') => body(field)
    .optional()
    .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
    .withMessage('Must be a valid hex color code'),
  
  // File size validation (in bytes)
  fileSize: (field, maxSize) => body(field)
    .optional()
    .custom((value) => {
      if (value && value.size > maxSize) {
        throw new Error(`File size must be less than ${maxSize} bytes`);
      }
      return true;
    }),
  
  // MIME type validation
  mimeType: (field, allowedTypes) => body(field)
    .optional()
    .custom((value) => {
      if (value && value.mimetype && !allowedTypes.includes(value.mimetype)) {
        throw new Error(`File type must be one of: ${allowedTypes.join(', ')}`);
      }
      return true;
    })
};

// Validation result handler middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.param,
      message: error.msg,
      value: error.value,
      location: error.location
    }));
    
    return next(new AppError('Validation failed', 400, true, formattedErrors));
  }
  
  next();
};

// Sanitization middleware
const sanitizeInput = (req, res, next) => {
  // Remove any null bytes
  const sanitizeValue = (value) => {
    if (typeof value === 'string') {
      return value.replace(/\0/g, '');
    }
    if (typeof value === 'object' && value !== null) {
      for (const key in value) {
        value[key] = sanitizeValue(value[key]);
      }
    }
    return value;
  };
  
  req.body = sanitizeValue(req.body);
  req.query = sanitizeValue(req.query);
  req.params = sanitizeValue(req.params);
  
  next();
};

// XSS protection middleware
const xssProtection = (req, res, next) => {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi
  ];
  
  const checkForXSS = (value) => {
    if (typeof value === 'string') {
      for (const pattern of xssPatterns) {
        if (pattern.test(value)) {
          return true;
        }
      }
    }
    if (typeof value === 'object' && value !== null) {
      for (const key in value) {
        if (checkForXSS(value[key])) {
          return true;
        }
      }
    }
    return false;
  };
  
  if (checkForXSS(req.body) || checkForXSS(req.query) || checkForXSS(req.params)) {
    return next(new AppError('Potentially malicious content detected', 400));
  }
  
  next();
};

// SQL injection protection middleware
const sqlInjectionProtection = (req, res, next) => {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(\b(OR|AND)\b.*=.*)/gi,
    /(--|\/\*|\*\/|;)/g,
    /(\b(CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\()/gi,
    /(\b(CAST|CONVERT)\s*\()/gi
  ];
  
  const checkForSQLInjection = (value) => {
    if (typeof value === 'string') {
      for (const pattern of sqlPatterns) {
        if (pattern.test(value)) {
          return true;
        }
      }
    }
    if (typeof value === 'object' && value !== null) {
      for (const key in value) {
        if (checkForSQLInjection(value[key])) {
          return true;
        }
      }
    }
    return false;
  };
  
  if (checkForSQLInjection(req.body) || checkForSQLInjection(req.query) || checkForSQLInjection(req.params)) {
    return next(new AppError('Potentially malicious SQL detected', 400));
  }
  
  next();
};

// Content type validation middleware
const validateContentType = (allowedTypes = ['application/json']) => {
  return (req, res, next) => {
    if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
      const contentType = req.get('Content-Type');
      
      if (!contentType) {
        return next(new AppError('Content-Type header is required', 400));
      }
      
      const isAllowed = allowedTypes.some(type => contentType.includes(type));
      
      if (!isAllowed) {
        return next(new AppError(`Content-Type must be one of: ${allowedTypes.join(', ')}`, 415));
      }
    }
    
    next();
  };
};

// Request size validation middleware
const validateRequestSize = (maxSize = 10 * 1024 * 1024) => { // 10MB default
  return (req, res, next) => {
    const contentLength = req.get('Content-Length');
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      return next(new AppError(`Request size exceeds maximum allowed size of ${maxSize} bytes`, 413));
    }
    
    next();
  };
};

// Custom validation middleware factory
const createCustomValidation = (validationRules) => {
  return [
    ...validationRules,
    handleValidationErrors
  ];
};

module.exports = {
  commonValidations,
  handleValidationErrors,
  sanitizeInput,
  xssProtection,
  sqlInjectionProtection,
  validateContentType,
  validateRequestSize,
  createCustomValidation
};
