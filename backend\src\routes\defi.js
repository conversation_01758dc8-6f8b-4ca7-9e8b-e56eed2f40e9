const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');

const db = require('../config/database');
const logger = require('../config/logger');
const { catchAsync } = require('../middleware/errorHandler');
const PriceService = require('../services/price');

const router = express.Router();
const priceService = new PriceService();

// Validation middleware
const createPositionValidation = [
  body('protocol').isLength({ min: 1, max: 100 }).trim(),
  body('protocol_version').optional().isLength({ max: 20 }),
  body('network').isLength({ min: 1, max: 50 }).trim(),
  body('type').isIn(['lending', 'borrowing', 'liquidity_pool', 'staking', 'farming', 'vault', 'options', 'futures']),
  body('primary_token').isLength({ min: 1, max: 20 }).trim().toUpperCase(),
  body('secondary_token').optional().isLength({ max: 20 }).trim().toUpperCase(),
  body('primary_amount').isFloat({ min: 0 }),
  body('secondary_amount').optional().isFloat({ min: 0 }),
  body('contract_address').optional().isLength({ max: 100 }),
  body('position_id').optional().isLength({ max: 255 }),
  body('portfolio_id').optional().isUUID(),
];

const updatePositionValidation = [
  body('primary_amount').optional().isFloat({ min: 0 }),
  body('secondary_amount').optional().isFloat({ min: 0 }),
  body('status').optional().isIn(['active', 'closed', 'liquidated', 'expired']),
  body('current_apy').optional().isFloat(),
  body('unclaimed_rewards').optional().isFloat({ min: 0 }),
  body('notes').optional().isLength({ max: 1000 }),
];

// Get all DeFi positions for user
router.get('/positions', catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 50, 
    protocol, 
    network, 
    type, 
    status = 'active',
    portfolio_id 
  } = req.query;
  
  const offset = (page - 1) * limit;

  let query = db('defi_positions')
    .where({ user_id: req.user.id, is_deleted: false })
    .limit(limit)
    .offset(offset)
    .orderBy('created_at', 'desc');

  if (protocol) {
    query = query.where({ protocol });
  }

  if (network) {
    query = query.where({ network });
  }

  if (type) {
    query = query.where({ type });
  }

  if (status !== 'all') {
    query = query.where({ status });
  }

  if (portfolio_id) {
    query = query.where({ portfolio_id });
  }

  const [positions, totalCount] = await Promise.all([
    query,
    db('defi_positions')
      .where({ user_id: req.user.id, is_deleted: false })
      .count('id as count')
      .first()
  ]);

  // Calculate current values and metrics
  const positionsWithValues = await Promise.all(
    positions.map(async (position) => {
      const primaryPrice = await priceService.getCurrentPrice(position.primary_token);
      const secondaryPrice = position.secondary_token 
        ? await priceService.getCurrentPrice(position.secondary_token) 
        : null;

      const primaryValue = position.primary_amount * (primaryPrice || 0);
      const secondaryValue = position.secondary_amount && secondaryPrice 
        ? position.secondary_amount * secondaryPrice 
        : 0;
      
      const currentValue = primaryValue + secondaryValue;
      const pnl = currentValue - (position.initial_value_usd || 0);
      const pnlPercentage = position.initial_value_usd > 0 
        ? (pnl / position.initial_value_usd) * 100 
        : 0;

      return {
        ...position,
        primary_price: primaryPrice,
        secondary_price: secondaryPrice,
        current_value_usd: currentValue,
        pnl_usd: pnl,
        pnl_percentage: pnlPercentage,
        risk_factors: position.risk_factors ? JSON.parse(position.risk_factors) : null,
        metadata: position.metadata ? JSON.parse(position.metadata) : null,
        tags: position.tags ? JSON.parse(position.tags) : null
      };
    })
  );

  res.json({
    message: 'DeFi positions retrieved successfully',
    data: {
      positions: positionsWithValues,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(totalCount.count),
        pages: Math.ceil(totalCount.count / limit)
      }
    }
  });
}));

// Get single DeFi position
router.get('/positions/:id', catchAsync(async (req, res) => {
  const position = await db('defi_positions')
    .where({ id: req.params.id, user_id: req.user.id, is_deleted: false })
    .first();

  if (!position) {
    return res.status(404).json({
      error: 'Position not found',
      message: 'DeFi position not found or access denied'
    });
  }

  // Calculate current values
  const primaryPrice = await priceService.getCurrentPrice(position.primary_token);
  const secondaryPrice = position.secondary_token 
    ? await priceService.getCurrentPrice(position.secondary_token) 
    : null;

  const primaryValue = position.primary_amount * (primaryPrice || 0);
  const secondaryValue = position.secondary_amount && secondaryPrice 
    ? position.secondary_amount * secondaryPrice 
    : 0;
  
  const currentValue = primaryValue + secondaryValue;
  const pnl = currentValue - (position.initial_value_usd || 0);
  const pnlPercentage = position.initial_value_usd > 0 
    ? (pnl / position.initial_value_usd) * 100 
    : 0;

  const positionWithValues = {
    ...position,
    primary_price: primaryPrice,
    secondary_price: secondaryPrice,
    current_value_usd: currentValue,
    pnl_usd: pnl,
    pnl_percentage: pnlPercentage,
    risk_factors: position.risk_factors ? JSON.parse(position.risk_factors) : null,
    metadata: position.metadata ? JSON.parse(position.metadata) : null,
    tags: position.tags ? JSON.parse(position.tags) : null
  };

  res.json({
    message: 'DeFi position retrieved successfully',
    data: positionWithValues
  });
}));

// Create new DeFi position
router.post('/positions', createPositionValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const {
    protocol,
    protocol_version,
    network,
    type,
    primary_token,
    secondary_token,
    primary_amount,
    secondary_amount,
    contract_address,
    position_id,
    portfolio_id,
    entry_tx_hash,
    collateral_amount,
    borrowed_amount,
    health_factor,
    liquidation_threshold,
    interest_rate,
    current_apy,
    reward_token,
    pool_share_percentage,
    risk_level,
    risk_factors,
    notes,
    tags
  } = req.body;

  // Verify portfolio ownership if provided
  if (portfolio_id) {
    const portfolio = await db('portfolios')
      .where({ id: portfolio_id, user_id: req.user.id, is_deleted: false })
      .first();
    
    if (!portfolio) {
      return res.status(400).json({
        error: 'Invalid portfolio',
        message: 'Portfolio not found or access denied'
      });
    }
  }

  // Calculate initial value
  const primaryPrice = await priceService.getCurrentPrice(primary_token);
  const secondaryPrice = secondary_token 
    ? await priceService.getCurrentPrice(secondary_token) 
    : null;

  const primaryValue = primary_amount * (primaryPrice || 0);
  const secondaryValue = secondary_amount && secondaryPrice 
    ? secondary_amount * secondaryPrice 
    : 0;
  
  const initialValue = primaryValue + secondaryValue;

  const positionData = {
    id: uuidv4(),
    user_id: req.user.id,
    portfolio_id: portfolio_id || null,
    protocol,
    protocol_version,
    network,
    type,
    primary_token: primary_token.toUpperCase(),
    secondary_token: secondary_token ? secondary_token.toUpperCase() : null,
    primary_amount,
    secondary_amount: secondary_amount || null,
    contract_address,
    position_id,
    initial_value_usd: initialValue,
    current_value_usd: initialValue,
    entry_tx_hash,
    entered_at: new Date(),
    collateral_amount: collateral_amount || null,
    borrowed_amount: borrowed_amount || null,
    health_factor: health_factor || null,
    liquidation_threshold: liquidation_threshold || null,
    interest_rate: interest_rate || null,
    current_apy: current_apy || null,
    reward_token,
    pool_share_percentage: pool_share_percentage || null,
    risk_level,
    risk_factors: risk_factors ? JSON.stringify(risk_factors) : null,
    notes,
    tags: tags ? JSON.stringify(tags) : null,
    metadata: JSON.stringify({
      entry_primary_price: primaryPrice,
      entry_secondary_price: secondaryPrice,
      created_at: new Date().toISOString()
    }),
    last_updated: new Date(),
    created_at: new Date(),
    updated_at: new Date()
  };

  await db('defi_positions').insert(positionData);

  logger.business('DeFi position created', { 
    userId: req.user.id, 
    positionId: positionData.id, 
    protocol, 
    type, 
    primary_token 
  });

  res.status(201).json({
    message: 'DeFi position created successfully',
    data: {
      ...positionData,
      risk_factors: positionData.risk_factors ? JSON.parse(positionData.risk_factors) : null,
      metadata: JSON.parse(positionData.metadata),
      tags: positionData.tags ? JSON.parse(positionData.tags) : null
    }
  });
}));

// Update DeFi position
router.put('/positions/:id', updatePositionValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const positionId = req.params.id;
  
  // Check if position exists and belongs to user
  const existingPosition = await db('defi_positions')
    .where({ id: positionId, user_id: req.user.id, is_deleted: false })
    .first();

  if (!existingPosition) {
    return res.status(404).json({
      error: 'Position not found',
      message: 'DeFi position not found or access denied'
    });
  }

  const updateData = {
    updated_at: new Date(),
    last_updated: new Date()
  };

  // Update allowed fields
  const allowedFields = [
    'primary_amount', 'secondary_amount', 'status', 'current_apy', 
    'unclaimed_rewards', 'total_rewards_earned', 'fees_earned',
    'collateral_amount', 'borrowed_amount', 'health_factor',
    'interest_rate', 'pool_share_percentage', 'notes', 'tags',
    'exit_tx_hash', 'gas_fees_paid'
  ];

  allowedFields.forEach(field => {
    if (req.body[field] !== undefined) {
      if (field === 'tags') {
        updateData[field] = JSON.stringify(req.body[field]);
      } else {
        updateData[field] = req.body[field];
      }
    }
  });

  // Set exit date if status is being changed to closed/liquidated/expired
  if (req.body.status && ['closed', 'liquidated', 'expired'].includes(req.body.status)) {
    updateData.exited_at = new Date();
  }

  await db('defi_positions')
    .where({ id: positionId })
    .update(updateData);

  logger.business('DeFi position updated', { userId: req.user.id, positionId });

  res.json({
    message: 'DeFi position updated successfully',
    data: updateData
  });
}));

// Delete DeFi position
router.delete('/positions/:id', catchAsync(async (req, res) => {
  const positionId = req.params.id;
  
  const result = await db('defi_positions')
    .where({ id: positionId, user_id: req.user.id, is_deleted: false })
    .update({
      is_deleted: true,
      deleted_at: new Date(),
      updated_at: new Date()
    });

  if (result === 0) {
    return res.status(404).json({
      error: 'Position not found',
      message: 'DeFi position not found or access denied'
    });
  }

  logger.business('DeFi position deleted', { userId: req.user.id, positionId });

  res.json({
    message: 'DeFi position deleted successfully'
  });
}));

// Get DeFi analytics
router.get('/analytics', catchAsync(async (req, res) => {
  const { timeframe = '30d', protocol, network } = req.query;

  let query = db('defi_positions')
    .where({ user_id: req.user.id, is_deleted: false });

  if (protocol) {
    query = query.where({ protocol });
  }

  if (network) {
    query = query.where({ network });
  }

  const positions = await query;

  // Calculate analytics
  let totalValue = 0;
  let totalRewards = 0;
  let totalFees = 0;
  let totalGasFees = 0;
  const protocolBreakdown = {};
  const networkBreakdown = {};
  const typeBreakdown = {};

  for (const position of positions) {
    // Calculate current value
    const primaryPrice = await priceService.getCurrentPrice(position.primary_token);
    const secondaryPrice = position.secondary_token 
      ? await priceService.getCurrentPrice(position.secondary_token) 
      : null;

    const primaryValue = position.primary_amount * (primaryPrice || 0);
    const secondaryValue = position.secondary_amount && secondaryPrice 
      ? position.secondary_amount * secondaryPrice 
      : 0;
    
    const currentValue = primaryValue + secondaryValue;
    totalValue += currentValue;
    totalRewards += position.total_rewards_earned || 0;
    totalFees += position.fees_earned || 0;
    totalGasFees += position.gas_fees_paid || 0;

    // Protocol breakdown
    if (!protocolBreakdown[position.protocol]) {
      protocolBreakdown[position.protocol] = { count: 0, value: 0 };
    }
    protocolBreakdown[position.protocol].count++;
    protocolBreakdown[position.protocol].value += currentValue;

    // Network breakdown
    if (!networkBreakdown[position.network]) {
      networkBreakdown[position.network] = { count: 0, value: 0 };
    }
    networkBreakdown[position.network].count++;
    networkBreakdown[position.network].value += currentValue;

    // Type breakdown
    if (!typeBreakdown[position.type]) {
      typeBreakdown[position.type] = { count: 0, value: 0 };
    }
    typeBreakdown[position.type].count++;
    typeBreakdown[position.type].value += currentValue;
  }

  const analytics = {
    summary: {
      total_positions: positions.length,
      total_value_usd: totalValue,
      total_rewards_earned: totalRewards,
      total_fees_earned: totalFees,
      total_gas_fees: totalGasFees,
      net_profit: totalRewards + totalFees - totalGasFees
    },
    breakdown: {
      by_protocol: protocolBreakdown,
      by_network: networkBreakdown,
      by_type: typeBreakdown
    }
  };

  res.json({
    message: 'DeFi analytics retrieved successfully',
    data: analytics
  });
}));

// Get supported protocols
router.get('/protocols', catchAsync(async (req, res) => {
  const protocols = [
    {
      name: 'Uniswap',
      versions: ['v2', 'v3'],
      networks: ['ethereum', 'polygon', 'arbitrum'],
      types: ['liquidity_pool'],
      description: 'Decentralized exchange protocol'
    },
    {
      name: 'Aave',
      versions: ['v2', 'v3'],
      networks: ['ethereum', 'polygon', 'avalanche'],
      types: ['lending', 'borrowing'],
      description: 'Decentralized lending protocol'
    },
    {
      name: 'Compound',
      versions: ['v2', 'v3'],
      networks: ['ethereum'],
      types: ['lending', 'borrowing'],
      description: 'Algorithmic money market protocol'
    },
    {
      name: 'Curve',
      versions: ['v1'],
      networks: ['ethereum', 'polygon'],
      types: ['liquidity_pool'],
      description: 'Stablecoin-focused DEX'
    },
    {
      name: 'Yearn',
      versions: ['v2'],
      networks: ['ethereum'],
      types: ['vault'],
      description: 'Yield farming aggregator'
    }
  ];

  res.json({
    message: 'Supported protocols retrieved successfully',
    data: protocols
  });
}));

module.exports = router;
