/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('portfolio_holdings', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to portfolios
    table.uuid('portfolio_id').notNullable();
    table.foreign('portfolio_id').references('id').inTable('portfolios').onDelete('CASCADE');
    
    // Asset information
    table.string('symbol', 20).notNullable();
    table.string('name', 100).nullable();
    table.string('asset_type', 50).defaultTo('cryptocurrency'); // cryptocurrency, token, nft, etc.
    table.string('network', 50).nullable(); // ethereum, bitcoin, polygon, etc.
    table.string('contract_address', 100).nullable();
    
    // Holding details
    table.decimal('amount', 30, 18).notNullable();
    table.decimal('average_price', 20, 8).nullable();
    table.decimal('current_price', 20, 8).nullable();
    table.decimal('total_cost', 20, 8).nullable();
    table.decimal('current_value', 20, 8).nullable();
    table.decimal('pnl', 20, 8).nullable();
    table.decimal('pnl_percentage', 10, 4).nullable();
    
    // Exchange/wallet information
    table.string('exchange', 100).nullable();
    table.string('wallet_address', 100).nullable();
    table.string('wallet_type', 50).nullable(); // hot, cold, hardware, exchange
    
    // Staking information
    table.boolean('is_staked').defaultTo(false);
    table.decimal('staked_amount', 30, 18).defaultTo(0);
    table.decimal('staking_rewards', 20, 8).defaultTo(0);
    table.decimal('staking_apy', 10, 4).defaultTo(0);
    table.timestamp('staking_started_at').nullable();
    
    // DeFi information
    table.boolean('is_in_defi').defaultTo(false);
    table.string('defi_protocol', 100).nullable();
    table.decimal('liquidity_provided', 20, 8).defaultTo(0);
    table.decimal('yield_earned', 20, 8).defaultTo(0);
    table.decimal('impermanent_loss', 20, 8).defaultTo(0);
    
    // Performance tracking
    table.decimal('daily_change', 20, 8).defaultTo(0);
    table.decimal('daily_change_percentage', 10, 4).defaultTo(0);
    table.decimal('weekly_change', 20, 8).defaultTo(0);
    table.decimal('weekly_change_percentage', 10, 4).defaultTo(0);
    table.decimal('monthly_change', 20, 8).defaultTo(0);
    table.decimal('monthly_change_percentage', 10, 4).defaultTo(0);
    
    // Allocation
    table.decimal('target_allocation', 10, 4).nullable();
    table.decimal('current_allocation', 10, 4).nullable();
    table.decimal('allocation_drift', 10, 4).nullable();
    
    // Metadata
    table.text('notes').nullable();
    table.json('metadata').nullable();
    table.json('tags').nullable();
    table.timestamp('last_updated').nullable();
    table.timestamp('first_purchased').nullable();
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['portfolio_id']);
    table.index(['symbol']);
    table.index(['asset_type']);
    table.index(['exchange']);
    table.index(['is_staked']);
    table.index(['is_in_defi']);
    table.index(['created_at']);
    table.index(['is_deleted']);
    table.index(['portfolio_id', 'symbol']);
    table.index(['portfolio_id', 'is_deleted']);
    
    // Unique constraint for portfolio + symbol + exchange combination
    table.unique(['portfolio_id', 'symbol', 'exchange'], 'unique_portfolio_symbol_exchange');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('portfolio_holdings');
};
