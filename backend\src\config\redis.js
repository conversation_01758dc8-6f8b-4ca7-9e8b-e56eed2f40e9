const redis = require('redis');
const logger = require('./logger');

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

// Create Redis client
const client = redis.createClient({
  socket: {
    host: redisConfig.host,
    port: redisConfig.port,
    connectTimeout: redisConfig.connectTimeout,
    commandTimeout: redisConfig.commandTimeout,
    keepAlive: redisConfig.keepAlive,
  },
  password: redisConfig.password,
  database: redisConfig.db,
});

// Redis event handlers
client.on('connect', () => {
  logger.info('🔗 Redis client connecting...');
});

client.on('ready', () => {
  logger.info('✅ Redis client connected and ready');
});

client.on('error', (err) => {
  logger.error('❌ Redis client error:', err.message);
});

client.on('end', () => {
  logger.info('🔌 Redis client connection ended');
});

client.on('reconnecting', () => {
  logger.info('🔄 Redis client reconnecting...');
});

// Connect to Redis
(async () => {
  try {
    await client.connect();
  } catch (error) {
    logger.error('Failed to connect to Redis:', error.message);
    process.exit(1);
  }
})();

// Redis utility functions
const redisUtils = {
  // Set key with expiration
  async setex(key, seconds, value) {
    try {
      const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value;
      return await client.setEx(key, seconds, serializedValue);
    } catch (error) {
      logger.error('Redis SETEX error:', error);
      throw error;
    }
  },

  // Get key
  async get(key) {
    try {
      const value = await client.get(key);
      if (!value) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      logger.error('Redis GET error:', error);
      throw error;
    }
  },

  // Delete key
  async del(key) {
    try {
      return await client.del(key);
    } catch (error) {
      logger.error('Redis DEL error:', error);
      throw error;
    }
  },

  // Check if key exists
  async exists(key) {
    try {
      return await client.exists(key);
    } catch (error) {
      logger.error('Redis EXISTS error:', error);
      throw error;
    }
  },

  // Set hash field
  async hset(key, field, value) {
    try {
      const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value;
      return await client.hSet(key, field, serializedValue);
    } catch (error) {
      logger.error('Redis HSET error:', error);
      throw error;
    }
  },

  // Get hash field
  async hget(key, field) {
    try {
      const value = await client.hGet(key, field);
      if (!value) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      logger.error('Redis HGET error:', error);
      throw error;
    }
  },

  // Get all hash fields
  async hgetall(key) {
    try {
      const hash = await client.hGetAll(key);
      const result = {};
      
      for (const [field, value] of Object.entries(hash)) {
        try {
          result[field] = JSON.parse(value);
        } catch {
          result[field] = value;
        }
      }
      
      return result;
    } catch (error) {
      logger.error('Redis HGETALL error:', error);
      throw error;
    }
  },

  // Add to sorted set
  async zadd(key, score, member) {
    try {
      return await client.zAdd(key, { score, value: member });
    } catch (error) {
      logger.error('Redis ZADD error:', error);
      throw error;
    }
  },

  // Get sorted set range
  async zrange(key, start, stop, withScores = false) {
    try {
      if (withScores) {
        return await client.zRangeWithScores(key, start, stop);
      }
      return await client.zRange(key, start, stop);
    } catch (error) {
      logger.error('Redis ZRANGE error:', error);
      throw error;
    }
  },

  // Publish message
  async publish(channel, message) {
    try {
      const serializedMessage = typeof message === 'object' ? JSON.stringify(message) : message;
      return await client.publish(channel, serializedMessage);
    } catch (error) {
      logger.error('Redis PUBLISH error:', error);
      throw error;
    }
  },

  // Subscribe to channel
  async subscribe(channel, callback) {
    try {
      const subscriber = client.duplicate();
      await subscriber.connect();
      
      await subscriber.subscribe(channel, (message) => {
        try {
          const parsedMessage = JSON.parse(message);
          callback(parsedMessage);
        } catch {
          callback(message);
        }
      });
      
      return subscriber;
    } catch (error) {
      logger.error('Redis SUBSCRIBE error:', error);
      throw error;
    }
  }
};

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('Closing Redis connection...');
  await client.quit();
});

process.on('SIGINT', async () => {
  logger.info('Closing Redis connection...');
  await client.quit();
});

module.exports = {
  client,
  ...redisUtils
};
