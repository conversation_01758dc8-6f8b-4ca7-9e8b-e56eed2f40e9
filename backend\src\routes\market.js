const express = require('express');
const { query, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');

const db = require('../config/database');
const logger = require('../config/logger');
const { catchAsync } = require('../middleware/errorHandler');
const PriceService = require('../services/price');

const router = express.Router();
const priceService = new PriceService();

// Rate limiting for market data endpoints
const marketDataRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 60, // limit each IP to 60 requests per minute
  message: 'Too many market data requests, please try again later.',
});

// Apply rate limiting to all routes
router.use(marketDataRateLimit);

// Validation middleware
const priceValidation = [
  query('symbols').optional().isString(),
  query('vs_currency').optional().isIn(['USD', 'EUR', 'GBP', 'JPY', 'BTC', 'ETH']),
];

const historicalValidation = [
  query('symbol').isString().isLength({ min: 1, max: 20 }),
  query('days').optional().isInt({ min: 1, max: 365 }),
  query('interval').optional().isIn(['hourly', 'daily']),
];

// Get current prices for multiple symbols
router.get('/prices', priceValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { symbols, vs_currency = 'USD' } = req.query;
  
  if (!symbols) {
    return res.status(400).json({
      error: 'Missing symbols',
      message: 'Please provide symbols parameter'
    });
  }

  const symbolList = symbols.split(',').map(s => s.trim().toUpperCase());
  
  if (symbolList.length > 100) {
    return res.status(400).json({
      error: 'Too many symbols',
      message: 'Maximum 100 symbols allowed per request'
    });
  }

  const prices = await priceService.getCurrentPrices(symbolList);

  res.json({
    message: 'Prices retrieved successfully',
    data: {
      prices,
      vs_currency: vs_currency,
      timestamp: new Date().toISOString()
    }
  });
}));

// Get current price for single symbol
router.get('/price/:symbol', catchAsync(async (req, res) => {
  const symbol = req.params.symbol.toUpperCase();
  const { vs_currency = 'USD' } = req.query;

  const price = await priceService.getCurrentPrice(symbol);

  if (price === null) {
    return res.status(404).json({
      error: 'Symbol not found',
      message: `Price data not available for ${symbol}`
    });
  }

  res.json({
    message: 'Price retrieved successfully',
    data: {
      symbol,
      price,
      vs_currency,
      timestamp: new Date().toISOString()
    }
  });
}));

// Get historical prices
router.get('/historical/:symbol', historicalValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const symbol = req.params.symbol.toUpperCase();
  const { days = 30, interval = 'daily' } = req.query;

  const historicalData = await priceService.getHistoricalPrices(symbol, days, interval);

  if (!historicalData) {
    return res.status(404).json({
      error: 'Data not found',
      message: `Historical data not available for ${symbol}`
    });
  }

  res.json({
    message: 'Historical data retrieved successfully',
    data: {
      symbol,
      days: parseInt(days),
      interval,
      ...historicalData
    }
  });
}));

// Get market data from database
router.get('/data', catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 100, 
    sort = 'market_cap_rank',
    order = 'asc',
    search 
  } = req.query;
  
  const offset = (page - 1) * limit;

  let query = db('market_data')
    .select([
      'symbol', 'name', 'price_usd', 'market_cap', 'volume_24h',
      'price_change_24h', 'market_cap_rank', 'last_updated'
    ])
    .limit(limit)
    .offset(offset)
    .orderBy(sort, order);

  if (search) {
    query = query.where(function() {
      this.where('symbol', 'ilike', `%${search}%`)
        .orWhere('name', 'ilike', `%${search}%`);
    });
  }

  // Get latest data for each symbol
  query = query.whereIn('id', function() {
    this.select(db.raw('MAX(id)'))
      .from('market_data')
      .groupBy('symbol');
  });

  const [marketData, totalCount] = await Promise.all([
    query,
    db('market_data')
      .countDistinct('symbol as count')
      .first()
  ]);

  res.json({
    message: 'Market data retrieved successfully',
    data: {
      market_data: marketData,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(totalCount.count),
        pages: Math.ceil(totalCount.count / limit)
      }
    }
  });
}));

// Get detailed market data for specific symbol
router.get('/data/:symbol', catchAsync(async (req, res) => {
  const symbol = req.params.symbol.toUpperCase();

  const marketData = await db('market_data')
    .where({ symbol })
    .orderBy('fetched_at', 'desc')
    .first();

  if (!marketData) {
    return res.status(404).json({
      error: 'Market data not found',
      message: `Market data not available for ${symbol}`
    });
  }

  // Parse raw data if available
  const data = {
    ...marketData,
    raw_data: marketData.raw_data ? JSON.parse(marketData.raw_data) : null
  };

  res.json({
    message: 'Market data retrieved successfully',
    data
  });
}));

// Get global market statistics
router.get('/global', catchAsync(async (req, res) => {
  const globalData = await priceService.getGlobalMarketData();

  if (!globalData) {
    return res.status(503).json({
      error: 'Service unavailable',
      message: 'Global market data temporarily unavailable'
    });
  }

  res.json({
    message: 'Global market data retrieved successfully',
    data: globalData
  });
}));

// Get trending cryptocurrencies
router.get('/trending', catchAsync(async (req, res) => {
  const { limit = 10 } = req.query;

  // Get trending based on volume change and price change
  const trending = await db('market_data')
    .select([
      'symbol', 'name', 'price_usd', 'price_change_24h', 
      'volume_24h', 'volume_change_24h', 'market_cap_rank'
    ])
    .whereIn('id', function() {
      this.select(db.raw('MAX(id)'))
        .from('market_data')
        .groupBy('symbol');
    })
    .where('volume_change_24h', '>', 20) // Volume increased by more than 20%
    .orderBy('volume_change_24h', 'desc')
    .limit(limit);

  res.json({
    message: 'Trending cryptocurrencies retrieved successfully',
    data: trending
  });
}));

// Get top gainers
router.get('/gainers', catchAsync(async (req, res) => {
  const { limit = 10, timeframe = '24h' } = req.query;

  let priceChangeColumn;
  switch (timeframe) {
    case '1h':
      priceChangeColumn = 'price_change_1h';
      break;
    case '7d':
      priceChangeColumn = 'price_change_7d';
      break;
    case '30d':
      priceChangeColumn = 'price_change_30d';
      break;
    default:
      priceChangeColumn = 'price_change_24h';
  }

  const gainers = await db('market_data')
    .select([
      'symbol', 'name', 'price_usd', priceChangeColumn + ' as price_change',
      'volume_24h', 'market_cap_rank'
    ])
    .whereIn('id', function() {
      this.select(db.raw('MAX(id)'))
        .from('market_data')
        .groupBy('symbol');
    })
    .whereNotNull(priceChangeColumn)
    .where(priceChangeColumn, '>', 0)
    .orderBy(priceChangeColumn, 'desc')
    .limit(limit);

  res.json({
    message: 'Top gainers retrieved successfully',
    data: {
      timeframe,
      gainers
    }
  });
}));

// Get top losers
router.get('/losers', catchAsync(async (req, res) => {
  const { limit = 10, timeframe = '24h' } = req.query;

  let priceChangeColumn;
  switch (timeframe) {
    case '1h':
      priceChangeColumn = 'price_change_1h';
      break;
    case '7d':
      priceChangeColumn = 'price_change_7d';
      break;
    case '30d':
      priceChangeColumn = 'price_change_30d';
      break;
    default:
      priceChangeColumn = 'price_change_24h';
  }

  const losers = await db('market_data')
    .select([
      'symbol', 'name', 'price_usd', priceChangeColumn + ' as price_change',
      'volume_24h', 'market_cap_rank'
    ])
    .whereIn('id', function() {
      this.select(db.raw('MAX(id)'))
        .from('market_data')
        .groupBy('symbol');
    })
    .whereNotNull(priceChangeColumn)
    .where(priceChangeColumn, '<', 0)
    .orderBy(priceChangeColumn, 'asc')
    .limit(limit);

  res.json({
    message: 'Top losers retrieved successfully',
    data: {
      timeframe,
      losers
    }
  });
}));

// Search cryptocurrencies
router.get('/search', catchAsync(async (req, res) => {
  const { q, limit = 20 } = req.query;

  if (!q || q.length < 2) {
    return res.status(400).json({
      error: 'Invalid query',
      message: 'Search query must be at least 2 characters long'
    });
  }

  const results = await db('market_data')
    .select([
      'symbol', 'name', 'price_usd', 'market_cap_rank',
      'coingecko_id', 'coinmarketcap_id'
    ])
    .whereIn('id', function() {
      this.select(db.raw('MAX(id)'))
        .from('market_data')
        .groupBy('symbol');
    })
    .where(function() {
      this.where('symbol', 'ilike', `%${q}%`)
        .orWhere('name', 'ilike', `%${q}%`);
    })
    .orderBy('market_cap_rank', 'asc')
    .limit(limit);

  res.json({
    message: 'Search results retrieved successfully',
    data: {
      query: q,
      results
    }
  });
}));

// Get market statistics
router.get('/stats', catchAsync(async (req, res) => {
  const stats = await db('market_data')
    .whereIn('id', function() {
      this.select(db.raw('MAX(id)'))
        .from('market_data')
        .groupBy('symbol');
    })
    .select(
      db.raw('COUNT(*) as total_cryptocurrencies'),
      db.raw('SUM(market_cap) as total_market_cap'),
      db.raw('SUM(volume_24h) as total_volume_24h'),
      db.raw('AVG(price_change_24h) as avg_price_change_24h'),
      db.raw('COUNT(CASE WHEN price_change_24h > 0 THEN 1 END) as gainers_count'),
      db.raw('COUNT(CASE WHEN price_change_24h < 0 THEN 1 END) as losers_count')
    )
    .first();

  const formattedStats = {
    total_cryptocurrencies: parseInt(stats.total_cryptocurrencies) || 0,
    total_market_cap: parseFloat(stats.total_market_cap) || 0,
    total_volume_24h: parseFloat(stats.total_volume_24h) || 0,
    avg_price_change_24h: parseFloat(stats.avg_price_change_24h) || 0,
    gainers_count: parseInt(stats.gainers_count) || 0,
    losers_count: parseInt(stats.losers_count) || 0,
    market_sentiment: (parseInt(stats.gainers_count) || 0) > (parseInt(stats.losers_count) || 0) ? 'bullish' : 'bearish'
  };

  res.json({
    message: 'Market statistics retrieved successfully',
    data: formattedStats
  });
}));

module.exports = router;
