const axios = require('axios');
const redis = require('../config/redis');
const logger = require('../config/logger');

class PriceService {
  constructor() {
    this.coingeckoBaseUrl = 'https://api.coingecko.com/api/v3';
    this.coinmarketcapBaseUrl = 'https://pro-api.coinmarketcap.com/v1';
    this.requestTimeout = 10000; // 10 seconds
    this.cacheTimeout = 300; // 5 minutes
    
    // Rate limiting
    this.lastCoingeckoRequest = 0;
    this.lastCoinmarketcapRequest = 0;
    this.coingeckoRateLimit = 1000; // 1 second between requests
    this.coinmarketcapRateLimit = 1000; // 1 second between requests
  }

  // Get current price for a single symbol
  async getCurrentPrice(symbol) {
    try {
      const cacheKey = `price:${symbol.toLowerCase()}`;
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return cached.price;
      }

      // Try CoinGecko first (free tier)
      let price = await this.getPriceFromCoingecko(symbol);
      
      // Fallback to CoinMarketCap if CoinGecko fails
      if (!price && process.env.COINMARKETCAP_API_KEY) {
        price = await this.getPriceFromCoinmarketcap(symbol);
      }

      if (price) {
        await redis.setex(cacheKey, this.cacheTimeout, { 
          price, 
          timestamp: new Date().toISOString(),
          source: 'api'
        });
      }

      return price;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getCurrentPrice', symbol });
      return null;
    }
  }

  // Get current prices for multiple symbols
  async getCurrentPrices(symbols) {
    try {
      const prices = {};
      const uncachedSymbols = [];

      // Check cache first
      for (const symbol of symbols) {
        const cacheKey = `price:${symbol.toLowerCase()}`;
        const cached = await redis.get(cacheKey);
        
        if (cached) {
          prices[symbol] = cached.price;
        } else {
          uncachedSymbols.push(symbol);
        }
      }

      // Fetch uncached prices
      if (uncachedSymbols.length > 0) {
        const fetchedPrices = await this.getBulkPricesFromCoingecko(uncachedSymbols);
        
        // Cache and merge results
        for (const [symbol, price] of Object.entries(fetchedPrices)) {
          prices[symbol] = price;
          
          const cacheKey = `price:${symbol.toLowerCase()}`;
          await redis.setex(cacheKey, this.cacheTimeout, { 
            price, 
            timestamp: new Date().toISOString(),
            source: 'api'
          });
        }
      }

      return prices;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getCurrentPrices', symbols });
      return {};
    }
  }

  // Get historical prices
  async getHistoricalPrices(symbol, days = 30, interval = 'daily') {
    try {
      const cacheKey = `historical:${symbol.toLowerCase()}:${days}:${interval}`;
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      const data = await this.getHistoricalFromCoingecko(symbol, days, interval);
      
      if (data) {
        // Cache for longer period for historical data
        await redis.setex(cacheKey, 3600, data); // 1 hour
      }

      return data;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getHistoricalPrices', symbol, days });
      return null;
    }
  }

  // Get market data
  async getMarketData(symbol) {
    try {
      const cacheKey = `market:${symbol.toLowerCase()}`;
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      const data = await this.getMarketDataFromCoingecko(symbol);
      
      if (data) {
        await redis.setex(cacheKey, this.cacheTimeout, data);
      }

      return data;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getMarketData', symbol });
      return null;
    }
  }

  // CoinGecko API methods
  async getPriceFromCoingecko(symbol) {
    try {
      await this.rateLimitCoingecko();
      
      const response = await axios.get(`${this.coingeckoBaseUrl}/simple/price`, {
        params: {
          ids: this.symbolToCoingeckoId(symbol),
          vs_currencies: 'usd',
          include_24hr_change: true,
          include_market_cap: true,
          include_24hr_vol: true
        },
        timeout: this.requestTimeout
      });

      const coinId = this.symbolToCoingeckoId(symbol);
      const data = response.data[coinId];
      
      return data ? data.usd : null;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getPriceFromCoingecko', symbol });
      return null;
    }
  }

  async getBulkPricesFromCoingecko(symbols) {
    try {
      await this.rateLimitCoingecko();
      
      const coinIds = symbols.map(symbol => this.symbolToCoingeckoId(symbol)).join(',');
      
      const response = await axios.get(`${this.coingeckoBaseUrl}/simple/price`, {
        params: {
          ids: coinIds,
          vs_currencies: 'usd',
          include_24hr_change: true
        },
        timeout: this.requestTimeout
      });

      const prices = {};
      
      for (const symbol of symbols) {
        const coinId = this.symbolToCoingeckoId(symbol);
        const data = response.data[coinId];
        
        if (data) {
          prices[symbol] = data.usd;
        }
      }

      return prices;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getBulkPricesFromCoingecko', symbols });
      return {};
    }
  }

  async getHistoricalFromCoingecko(symbol, days, interval) {
    try {
      await this.rateLimitCoingecko();
      
      const coinId = this.symbolToCoingeckoId(symbol);
      const response = await axios.get(`${this.coingeckoBaseUrl}/coins/${coinId}/market_chart`, {
        params: {
          vs_currency: 'usd',
          days: days,
          interval: interval === 'hourly' ? 'hourly' : 'daily'
        },
        timeout: this.requestTimeout
      });

      return {
        prices: response.data.prices,
        market_caps: response.data.market_caps,
        total_volumes: response.data.total_volumes
      };
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getHistoricalFromCoingecko', symbol, days });
      return null;
    }
  }

  async getMarketDataFromCoingecko(symbol) {
    try {
      await this.rateLimitCoingecko();
      
      const coinId = this.symbolToCoingeckoId(symbol);
      const response = await axios.get(`${this.coingeckoBaseUrl}/coins/${coinId}`, {
        params: {
          localization: false,
          tickers: false,
          market_data: true,
          community_data: false,
          developer_data: false,
          sparkline: false
        },
        timeout: this.requestTimeout
      });

      const data = response.data;
      const marketData = data.market_data;

      return {
        id: data.id,
        symbol: data.symbol,
        name: data.name,
        current_price: marketData.current_price.usd,
        market_cap: marketData.market_cap.usd,
        market_cap_rank: marketData.market_cap_rank,
        total_volume: marketData.total_volume.usd,
        high_24h: marketData.high_24h.usd,
        low_24h: marketData.low_24h.usd,
        price_change_24h: marketData.price_change_24h,
        price_change_percentage_24h: marketData.price_change_percentage_24h,
        price_change_percentage_7d: marketData.price_change_percentage_7d,
        price_change_percentage_30d: marketData.price_change_percentage_30d,
        circulating_supply: marketData.circulating_supply,
        total_supply: marketData.total_supply,
        max_supply: marketData.max_supply,
        ath: marketData.ath.usd,
        ath_change_percentage: marketData.ath_change_percentage.usd,
        ath_date: marketData.ath_date.usd,
        atl: marketData.atl.usd,
        atl_change_percentage: marketData.atl_change_percentage.usd,
        atl_date: marketData.atl_date.usd,
        last_updated: marketData.last_updated
      };
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getMarketDataFromCoingecko', symbol });
      return null;
    }
  }

  // CoinMarketCap API methods (requires API key)
  async getPriceFromCoinmarketcap(symbol) {
    try {
      if (!process.env.COINMARKETCAP_API_KEY) {
        return null;
      }

      await this.rateLimitCoinmarketcap();
      
      const response = await axios.get(`${this.coinmarketcapBaseUrl}/cryptocurrency/quotes/latest`, {
        params: {
          symbol: symbol.toUpperCase(),
          convert: 'USD'
        },
        headers: {
          'X-CMC_PRO_API_KEY': process.env.COINMARKETCAP_API_KEY
        },
        timeout: this.requestTimeout
      });

      const data = response.data.data[symbol.toUpperCase()];
      return data ? data.quote.USD.price : null;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getPriceFromCoinmarketcap', symbol });
      return null;
    }
  }

  // Rate limiting helpers
  async rateLimitCoingecko() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastCoingeckoRequest;
    
    if (timeSinceLastRequest < this.coingeckoRateLimit) {
      const delay = this.coingeckoRateLimit - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    this.lastCoingeckoRequest = Date.now();
  }

  async rateLimitCoinmarketcap() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastCoinmarketcapRequest;
    
    if (timeSinceLastRequest < this.coinmarketcapRateLimit) {
      const delay = this.coinmarketcapRateLimit - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    
    this.lastCoinmarketcapRequest = Date.now();
  }

  // Helper to convert symbol to CoinGecko ID
  symbolToCoingeckoId(symbol) {
    const symbolMap = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'ADA': 'cardano',
      'DOT': 'polkadot',
      'LINK': 'chainlink',
      'LTC': 'litecoin',
      'BCH': 'bitcoin-cash',
      'XLM': 'stellar',
      'USDT': 'tether',
      'USDC': 'usd-coin',
      'BNB': 'binancecoin',
      'SOL': 'solana',
      'AVAX': 'avalanche-2',
      'MATIC': 'matic-network',
      'ATOM': 'cosmos',
      'ALGO': 'algorand',
      'VET': 'vechain',
      'ICP': 'internet-computer',
      'FTT': 'ftx-token',
      'THETA': 'theta-token',
      'TRX': 'tron',
      'ETC': 'ethereum-classic',
      'FIL': 'filecoin',
      'XMR': 'monero',
      'AAVE': 'aave',
      'MKR': 'maker',
      'COMP': 'compound-governance-token',
      'UNI': 'uniswap',
      'SUSHI': 'sushi',
      'CRV': 'curve-dao-token',
      'YFI': 'yearn-finance',
      'SNX': 'havven',
      '1INCH': '1inch',
      'DOGE': 'dogecoin',
      'SHIB': 'shiba-inu'
    };

    return symbolMap[symbol.toUpperCase()] || symbol.toLowerCase();
  }

  // Get trending coins
  async getTrendingCoins() {
    try {
      const cacheKey = 'trending_coins';
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      await this.rateLimitCoingecko();
      
      const response = await axios.get(`${this.coingeckoBaseUrl}/search/trending`, {
        timeout: this.requestTimeout
      });

      const trending = response.data.coins.map(coin => ({
        id: coin.item.id,
        symbol: coin.item.symbol,
        name: coin.item.name,
        market_cap_rank: coin.item.market_cap_rank,
        thumb: coin.item.thumb,
        score: coin.item.score
      }));

      // Cache for 1 hour
      await redis.setex(cacheKey, 3600, trending);

      return trending;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getTrendingCoins' });
      return [];
    }
  }

  // Get global market data
  async getGlobalMarketData() {
    try {
      const cacheKey = 'global_market_data';
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      await this.rateLimitCoingecko();
      
      const response = await axios.get(`${this.coingeckoBaseUrl}/global`, {
        timeout: this.requestTimeout
      });

      const data = response.data.data;
      const globalData = {
        active_cryptocurrencies: data.active_cryptocurrencies,
        upcoming_icos: data.upcoming_icos,
        ongoing_icos: data.ongoing_icos,
        ended_icos: data.ended_icos,
        markets: data.markets,
        total_market_cap: data.total_market_cap.usd,
        total_volume: data.total_volume.usd,
        market_cap_percentage: data.market_cap_percentage,
        market_cap_change_percentage_24h_usd: data.market_cap_change_percentage_24h_usd,
        updated_at: data.updated_at
      };

      // Cache for 10 minutes
      await redis.setex(cacheKey, 600, globalData);

      return globalData;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getGlobalMarketData' });
      return null;
    }
  }
}

module.exports = PriceService;
