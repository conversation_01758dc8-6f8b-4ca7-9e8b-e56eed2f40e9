const express = require('express');
const { query, validationResult } = require('express-validator');
const moment = require('moment');

const db = require('../config/database');
const logger = require('../config/logger');
const { catchAsync } = require('../middleware/errorHandler');
const PriceService = require('../services/price');

const router = express.Router();
const priceService = new PriceService();

// Validation middleware
const timeRangeValidation = [
  query('timeframe').optional().isIn(['1d', '7d', '30d', '90d', '1y', 'all']),
  query('portfolioId').optional().isUUID(),
];

// Get portfolio performance analytics
router.get('/portfolio/performance', timeRangeValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { timeframe = '30d', portfolioId } = req.query;
  
  // Get user's portfolios
  let portfolioQuery = db('portfolios')
    .where({ user_id: req.user.id, is_deleted: false });
  
  if (portfolioId) {
    portfolioQuery = portfolioQuery.where({ id: portfolioId });
  }
  
  const portfolios = await portfolioQuery;
  
  if (portfolios.length === 0) {
    return res.status(404).json({
      error: 'No portfolios found',
      message: 'No portfolios found for the specified criteria'
    });
  }

  // Calculate time range
  const endDate = moment();
  let startDate;
  
  switch (timeframe) {
    case '1d':
      startDate = moment().subtract(1, 'day');
      break;
    case '7d':
      startDate = moment().subtract(7, 'days');
      break;
    case '30d':
      startDate = moment().subtract(30, 'days');
      break;
    case '90d':
      startDate = moment().subtract(90, 'days');
      break;
    case '1y':
      startDate = moment().subtract(1, 'year');
      break;
    default:
      startDate = moment().subtract(30, 'days');
  }

  const performanceData = await Promise.all(
    portfolios.map(async (portfolio) => {
      const holdings = await db('portfolio_holdings')
        .where({ portfolio_id: portfolio.id, is_deleted: false });

      let totalCurrentValue = 0;
      let totalCost = 0;
      let holdingsData = [];

      for (const holding of holdings) {
        const currentPrice = await priceService.getCurrentPrice(holding.symbol);
        const currentValue = holding.amount * (currentPrice || 0);
        const cost = holding.amount * (holding.average_price || 0);
        
        totalCurrentValue += currentValue;
        totalCost += cost;
        
        holdingsData.push({
          symbol: holding.symbol,
          amount: holding.amount,
          current_price: currentPrice,
          current_value: currentValue,
          cost: cost,
          pnl: currentValue - cost,
          pnl_percentage: cost > 0 ? ((currentValue - cost) / cost) * 100 : 0,
          allocation: totalCurrentValue > 0 ? (currentValue / totalCurrentValue) * 100 : 0
        });
      }

      const totalPnl = totalCurrentValue - totalCost;
      const totalPnlPercentage = totalCost > 0 ? (totalPnl / totalCost) * 100 : 0;

      return {
        portfolio_id: portfolio.id,
        portfolio_name: portfolio.name,
        current_value: totalCurrentValue,
        total_cost: totalCost,
        total_pnl: totalPnl,
        total_pnl_percentage: totalPnlPercentage,
        holdings: holdingsData
      };
    })
  );

  // Calculate overall performance
  const overallStats = performanceData.reduce((acc, portfolio) => {
    acc.total_value += portfolio.current_value;
    acc.total_cost += portfolio.total_cost;
    acc.total_pnl += portfolio.total_pnl;
    return acc;
  }, { total_value: 0, total_cost: 0, total_pnl: 0 });

  overallStats.total_pnl_percentage = overallStats.total_cost > 0 
    ? (overallStats.total_pnl / overallStats.total_cost) * 100 
    : 0;

  res.json({
    message: 'Portfolio performance retrieved successfully',
    data: {
      timeframe,
      overall: overallStats,
      portfolios: performanceData
    }
  });
}));

// Get asset allocation analytics
router.get('/portfolio/allocation', catchAsync(async (req, res) => {
  const { portfolioId } = req.query;
  
  let portfolioQuery = db('portfolios')
    .where({ user_id: req.user.id, is_deleted: false });
  
  if (portfolioId) {
    portfolioQuery = portfolioQuery.where({ id: portfolioId });
  }
  
  const portfolios = await portfolioQuery;
  
  const allocationData = await Promise.all(
    portfolios.map(async (portfolio) => {
      const holdings = await db('portfolio_holdings')
        .where({ portfolio_id: portfolio.id, is_deleted: false });

      let totalValue = 0;
      const assetData = [];

      // Calculate current values
      for (const holding of holdings) {
        const currentPrice = await priceService.getCurrentPrice(holding.symbol);
        const currentValue = holding.amount * (currentPrice || 0);
        totalValue += currentValue;
        
        assetData.push({
          symbol: holding.symbol,
          name: holding.name,
          amount: holding.amount,
          current_value: currentValue,
          asset_type: holding.asset_type || 'cryptocurrency'
        });
      }

      // Calculate allocations
      const allocations = assetData.map(asset => ({
        ...asset,
        allocation_percentage: totalValue > 0 ? (asset.current_value / totalValue) * 100 : 0
      }));

      // Group by asset type
      const byAssetType = allocations.reduce((acc, asset) => {
        const type = asset.asset_type;
        if (!acc[type]) {
          acc[type] = { total_value: 0, allocation_percentage: 0, assets: [] };
        }
        acc[type].total_value += asset.current_value;
        acc[type].allocation_percentage += asset.allocation_percentage;
        acc[type].assets.push(asset);
        return acc;
      }, {});

      return {
        portfolio_id: portfolio.id,
        portfolio_name: portfolio.name,
        total_value: totalValue,
        allocations,
        by_asset_type: byAssetType
      };
    })
  );

  res.json({
    message: 'Asset allocation retrieved successfully',
    data: allocationData
  });
}));

// Get transaction analytics
router.get('/transactions', timeRangeValidation, catchAsync(async (req, res) => {
  const { timeframe = '30d', portfolioId } = req.query;
  
  // Calculate time range
  const endDate = moment();
  let startDate;
  
  switch (timeframe) {
    case '1d':
      startDate = moment().subtract(1, 'day');
      break;
    case '7d':
      startDate = moment().subtract(7, 'days');
      break;
    case '30d':
      startDate = moment().subtract(30, 'days');
      break;
    case '90d':
      startDate = moment().subtract(90, 'days');
      break;
    case '1y':
      startDate = moment().subtract(1, 'year');
      break;
    default:
      startDate = moment().subtract(30, 'days');
  }

  let transactionQuery = db('transactions')
    .where({ user_id: req.user.id, is_deleted: false })
    .whereBetween('transaction_date', [startDate.toDate(), endDate.toDate()]);
  
  if (portfolioId) {
    transactionQuery = transactionQuery.where({ portfolio_id: portfolioId });
  }

  const transactions = await transactionQuery.orderBy('transaction_date', 'desc');

  // Calculate analytics
  const analytics = {
    total_transactions: transactions.length,
    by_type: {},
    by_symbol: {},
    total_volume: 0,
    total_fees: 0,
    timeline: {}
  };

  transactions.forEach(tx => {
    // By type
    if (!analytics.by_type[tx.type]) {
      analytics.by_type[tx.type] = { count: 0, volume: 0 };
    }
    analytics.by_type[tx.type].count++;
    analytics.by_type[tx.type].volume += tx.total_value || 0;

    // By symbol
    if (!analytics.by_symbol[tx.symbol]) {
      analytics.by_symbol[tx.symbol] = { count: 0, volume: 0 };
    }
    analytics.by_symbol[tx.symbol].count++;
    analytics.by_symbol[tx.symbol].volume += tx.total_value || 0;

    // Totals
    analytics.total_volume += tx.total_value || 0;
    analytics.total_fees += tx.fee || 0;

    // Timeline (group by day)
    const day = moment(tx.transaction_date).format('YYYY-MM-DD');
    if (!analytics.timeline[day]) {
      analytics.timeline[day] = { count: 0, volume: 0 };
    }
    analytics.timeline[day].count++;
    analytics.timeline[day].volume += tx.total_value || 0;
  });

  res.json({
    message: 'Transaction analytics retrieved successfully',
    data: {
      timeframe,
      analytics,
      recent_transactions: transactions.slice(0, 10) // Last 10 transactions
    }
  });
}));

// Get profit/loss analytics
router.get('/pnl', timeRangeValidation, catchAsync(async (req, res) => {
  const { timeframe = '30d', portfolioId } = req.query;
  
  // Get realized P&L from transactions
  let transactionQuery = db('transactions')
    .where({ user_id: req.user.id, is_deleted: false })
    .whereNotNull('realized_pnl');
  
  if (portfolioId) {
    transactionQuery = transactionQuery.where({ portfolio_id: portfolioId });
  }

  const transactions = await transactionQuery;

  // Calculate realized P&L
  const realizedPnl = transactions.reduce((acc, tx) => {
    acc.total += tx.realized_pnl || 0;
    
    const symbol = tx.symbol;
    if (!acc.by_symbol[symbol]) {
      acc.by_symbol[symbol] = 0;
    }
    acc.by_symbol[symbol] += tx.realized_pnl || 0;
    
    return acc;
  }, { total: 0, by_symbol: {} });

  // Get unrealized P&L from current holdings
  let portfolioQuery = db('portfolios')
    .where({ user_id: req.user.id, is_deleted: false });
  
  if (portfolioId) {
    portfolioQuery = portfolioQuery.where({ id: portfolioId });
  }
  
  const portfolios = await portfolioQuery;
  
  let unrealizedPnl = { total: 0, by_symbol: {} };
  
  for (const portfolio of portfolios) {
    const holdings = await db('portfolio_holdings')
      .where({ portfolio_id: portfolio.id, is_deleted: false });

    for (const holding of holdings) {
      const currentPrice = await priceService.getCurrentPrice(holding.symbol);
      const currentValue = holding.amount * (currentPrice || 0);
      const cost = holding.amount * (holding.average_price || 0);
      const pnl = currentValue - cost;
      
      unrealizedPnl.total += pnl;
      
      if (!unrealizedPnl.by_symbol[holding.symbol]) {
        unrealizedPnl.by_symbol[holding.symbol] = 0;
      }
      unrealizedPnl.by_symbol[holding.symbol] += pnl;
    }
  }

  const totalPnl = realizedPnl.total + unrealizedPnl.total;

  res.json({
    message: 'P&L analytics retrieved successfully',
    data: {
      timeframe,
      realized_pnl: realizedPnl,
      unrealized_pnl: unrealizedPnl,
      total_pnl: totalPnl
    }
  });
}));

// Get market comparison analytics
router.get('/market-comparison', catchAsync(async (req, res) => {
  const { portfolioId, benchmark = 'BTC' } = req.query;
  
  // Get portfolio performance
  let portfolioQuery = db('portfolios')
    .where({ user_id: req.user.id, is_deleted: false });
  
  if (portfolioId) {
    portfolioQuery = portfolioQuery.where({ id: portfolioId });
  }
  
  const portfolios = await portfolioQuery;
  
  if (portfolios.length === 0) {
    return res.status(404).json({
      error: 'No portfolios found',
      message: 'No portfolios found for comparison'
    });
  }

  // Calculate portfolio performance
  let totalPortfolioValue = 0;
  let totalPortfolioCost = 0;
  
  for (const portfolio of portfolios) {
    totalPortfolioValue += portfolio.current_value || 0;
    totalPortfolioCost += portfolio.total_invested || 0;
  }
  
  const portfolioReturn = totalPortfolioCost > 0 
    ? ((totalPortfolioValue - totalPortfolioCost) / totalPortfolioCost) * 100 
    : 0;

  // Get benchmark performance (simplified - would need historical data for accurate comparison)
  const benchmarkPrice = await priceService.getCurrentPrice(benchmark);
  const benchmarkHistorical = await priceService.getHistoricalPrices(benchmark, 30);
  
  let benchmarkReturn = 0;
  if (benchmarkHistorical && benchmarkHistorical.prices && benchmarkHistorical.prices.length > 0) {
    const oldPrice = benchmarkHistorical.prices[0][1];
    benchmarkReturn = ((benchmarkPrice - oldPrice) / oldPrice) * 100;
  }

  const comparison = {
    portfolio: {
      total_value: totalPortfolioValue,
      total_cost: totalPortfolioCost,
      return_percentage: portfolioReturn
    },
    benchmark: {
      symbol: benchmark,
      current_price: benchmarkPrice,
      return_percentage: benchmarkReturn
    },
    relative_performance: portfolioReturn - benchmarkReturn
  };

  res.json({
    message: 'Market comparison retrieved successfully',
    data: comparison
  });
}));

// Get risk metrics
router.get('/risk', catchAsync(async (req, res) => {
  const { portfolioId } = req.query;
  
  let portfolioQuery = db('portfolios')
    .where({ user_id: req.user.id, is_deleted: false });
  
  if (portfolioId) {
    portfolioQuery = portfolioQuery.where({ id: portfolioId });
  }
  
  const portfolios = await portfolioQuery;
  
  const riskMetrics = await Promise.all(
    portfolios.map(async (portfolio) => {
      const holdings = await db('portfolio_holdings')
        .where({ portfolio_id: portfolio.id, is_deleted: false });

      let totalValue = 0;
      const assetRisks = [];

      for (const holding of holdings) {
        const currentPrice = await priceService.getCurrentPrice(holding.symbol);
        const currentValue = holding.amount * (currentPrice || 0);
        totalValue += currentValue;
        
        // Get historical data for volatility calculation
        const historical = await priceService.getHistoricalPrices(holding.symbol, 30);
        let volatility = 0;
        
        if (historical && historical.prices && historical.prices.length > 1) {
          const returns = [];
          for (let i = 1; i < historical.prices.length; i++) {
            const prevPrice = historical.prices[i - 1][1];
            const currPrice = historical.prices[i][1];
            returns.push((currPrice - prevPrice) / prevPrice);
          }
          
          // Calculate standard deviation (volatility)
          const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
          const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
          volatility = Math.sqrt(variance) * Math.sqrt(365) * 100; // Annualized volatility
        }
        
        assetRisks.push({
          symbol: holding.symbol,
          allocation: totalValue > 0 ? (currentValue / totalValue) * 100 : 0,
          volatility,
          current_value: currentValue
        });
      }

      // Calculate portfolio-level risk metrics
      const portfolioVolatility = assetRisks.reduce((acc, asset) => {
        return acc + (asset.volatility * asset.allocation / 100);
      }, 0);

      // Diversification score (simplified)
      const diversificationScore = Math.min(100, holdings.length * 10);

      return {
        portfolio_id: portfolio.id,
        portfolio_name: portfolio.name,
        total_value: totalValue,
        volatility: portfolioVolatility,
        diversification_score: diversificationScore,
        asset_risks: assetRisks,
        risk_level: portfolioVolatility < 20 ? 'Low' : portfolioVolatility < 40 ? 'Medium' : 'High'
      };
    })
  );

  res.json({
    message: 'Risk metrics retrieved successfully',
    data: riskMetrics
  });
}));

module.exports = router;
