const cron = require('node-cron');
const logger = require('../config/logger');
const db = require('../config/database');
const PriceService = require('./price');
const EmailService = require('./email');
const NotificationService = require('./notification');

class CronService {
  constructor() {
    this.priceService = new PriceService();
    this.emailService = new EmailService();
    this.notificationService = new NotificationService();
    this.jobs = new Map();
    this.isRunning = false;
  }

  start() {
    if (this.isRunning) {
      logger.warn('Cron service is already running');
      return;
    }

    this.isRunning = true;
    
    // Update market data every 5 minutes
    this.scheduleJob('updateMarketData', '*/5 * * * *', () => {
      this.updateMarketData();
    });

    // Check price alerts every minute
    this.scheduleJob('checkPriceAlerts', '* * * * *', () => {
      this.checkPriceAlerts();
    });

    // Update portfolio values every 10 minutes
    this.scheduleJob('updatePortfolioValues', '*/10 * * * *', () => {
      this.updatePortfolioValues();
    });

    // Clean up expired tokens daily at 2 AM
    this.scheduleJob('cleanupExpiredTokens', '0 2 * * *', () => {
      this.cleanupExpiredTokens();
    });

    // Send daily portfolio summaries at 9 AM
    this.scheduleJob('sendDailyPortfolioSummaries', '0 9 * * *', () => {
      this.sendDailyPortfolioSummaries();
    });

    // Clean up old notifications weekly
    this.scheduleJob('cleanupOldNotifications', '0 3 * * 0', () => {
      this.cleanupOldNotifications();
    });

    // Update DeFi positions every 15 minutes
    this.scheduleJob('updateDefiPositions', '*/15 * * * *', () => {
      this.updateDefiPositions();
    });

    // Archive old audit logs monthly
    this.scheduleJob('archiveOldAuditLogs', '0 4 1 * *', () => {
      this.archiveOldAuditLogs();
    });

    logger.info('Cron service started with all scheduled jobs');
  }

  scheduleJob(name, schedule, task) {
    try {
      const job = cron.schedule(schedule, async () => {
        const startTime = Date.now();
        logger.info(`Starting cron job: ${name}`);
        
        try {
          await task();
          const duration = Date.now() - startTime;
          logger.info(`Completed cron job: ${name} (${duration}ms)`);
        } catch (error) {
          logger.errorWithContext(error, { cronJob: name });
        }
      }, {
        scheduled: false,
        timezone: 'UTC'
      });

      this.jobs.set(name, job);
      job.start();
      
      logger.info(`Scheduled cron job: ${name} with schedule: ${schedule}`);
    } catch (error) {
      logger.errorWithContext(error, { cronJob: name, schedule });
    }
  }

  async updateMarketData() {
    try {
      // Get all unique symbols from portfolios and watchlists
      const symbols = await this.getActiveSymbols();
      
      if (symbols.length === 0) {
        return;
      }

      // Fetch current market data
      const marketData = await this.priceService.getMarketDataBulk(symbols);
      
      // Update database
      for (const data of marketData) {
        await db('market_data')
          .insert({
            ...data,
            fetched_at: new Date(),
            created_at: new Date(),
            updated_at: new Date()
          })
          .onConflict(['symbol', 'fetched_at'])
          .merge();
      }

      logger.info(`Updated market data for ${marketData.length} symbols`);
    } catch (error) {
      logger.errorWithContext(error, { cronJob: 'updateMarketData' });
    }
  }

  async checkPriceAlerts() {
    try {
      // Get all active alerts
      const alerts = await db('alerts')
        .where({ is_active: true, is_triggered: false })
        .whereRaw('(expires_at IS NULL OR expires_at > NOW())')
        .whereRaw('(last_triggered IS NULL OR last_triggered < NOW() - INTERVAL ? MINUTE)', [60]); // Respect cooldown

      for (const alert of alerts) {
        const currentPrice = await this.priceService.getCurrentPrice(alert.symbol);
        
        if (!currentPrice) {
          continue;
        }

        const shouldTrigger = this.evaluateAlertCondition(alert, currentPrice);
        
        if (shouldTrigger) {
          await this.triggerAlert(alert, currentPrice);
        }

        // Update last checked time
        await db('alerts')
          .where({ id: alert.id })
          .update({ 
            last_checked: new Date(),
            current_value: currentPrice
          });
      }
    } catch (error) {
      logger.errorWithContext(error, { cronJob: 'checkPriceAlerts' });
    }
  }

  evaluateAlertCondition(alert, currentPrice) {
    const { condition, target_value } = alert;
    
    switch (condition) {
      case 'above':
        return currentPrice > target_value;
      case 'below':
        return currentPrice < target_value;
      case 'crosses_above':
        return currentPrice > target_value && (alert.current_value || 0) <= target_value;
      case 'crosses_below':
        return currentPrice < target_value && (alert.current_value || 0) >= target_value;
      case 'equals':
        return Math.abs(currentPrice - target_value) < (target_value * 0.01); // 1% tolerance
      default:
        return false;
    }
  }

  async triggerAlert(alert, currentPrice) {
    try {
      // Update alert status
      await db('alerts')
        .where({ id: alert.id })
        .update({
          is_triggered: true,
          last_triggered: new Date(),
          trigger_count: db.raw('trigger_count + 1'),
          trigger_value: currentPrice
        });

      // Send notifications
      const notificationMethods = alert.notification_methods || [];
      
      const notification = {
        user_id: alert.user_id,
        title: `Price Alert: ${alert.symbol}`,
        message: `${alert.symbol} has ${alert.condition} ${alert.target_value} ${alert.base_currency}. Current price: ${currentPrice}`,
        type: 'price_alert',
        channels: notificationMethods,
        related_entity_type: 'alert',
        related_entity_id: alert.id,
        metadata: {
          symbol: alert.symbol,
          condition: alert.condition,
          target_value: alert.target_value,
          current_price: currentPrice,
          alert_name: alert.name
        }
      };

      await this.notificationService.createNotification(notification);

      // Reset alert if not recurring
      if (!alert.is_recurring) {
        await db('alerts')
          .where({ id: alert.id })
          .update({ is_active: false });
      }

      // Check max triggers limit
      if (alert.max_triggers && alert.trigger_count >= alert.max_triggers) {
        await db('alerts')
          .where({ id: alert.id })
          .update({ is_active: false });
      }

      logger.info(`Alert triggered: ${alert.name} for ${alert.symbol} at ${currentPrice}`);
    } catch (error) {
      logger.errorWithContext(error, { alertId: alert.id, currentPrice });
    }
  }

  async updatePortfolioValues() {
    try {
      // Get all active portfolios
      const portfolios = await db('portfolios')
        .where({ is_deleted: false });

      for (const portfolio of portfolios) {
        await this.updateSinglePortfolioValue(portfolio.id);
      }

      logger.info(`Updated values for ${portfolios.length} portfolios`);
    } catch (error) {
      logger.errorWithContext(error, { cronJob: 'updatePortfolioValues' });
    }
  }

  async updateSinglePortfolioValue(portfolioId) {
    try {
      const holdings = await db('portfolio_holdings')
        .where({ portfolio_id: portfolioId, is_deleted: false });

      let totalValue = 0;
      let totalCost = 0;

      for (const holding of holdings) {
        const currentPrice = await this.priceService.getCurrentPrice(holding.symbol);
        
        if (currentPrice) {
          const holdingValue = holding.amount * currentPrice;
          const holdingCost = holding.amount * (holding.average_price || 0);
          
          totalValue += holdingValue;
          totalCost += holdingCost;

          // Update holding values
          await db('portfolio_holdings')
            .where({ id: holding.id })
            .update({
              current_price: currentPrice,
              current_value: holdingValue,
              pnl: holdingValue - holdingCost,
              pnl_percentage: holdingCost > 0 ? ((holdingValue - holdingCost) / holdingCost) * 100 : 0,
              last_updated: new Date()
            });
        }
      }

      const totalPnl = totalValue - totalCost;
      const totalPnlPercentage = totalCost > 0 ? (totalPnl / totalCost) * 100 : 0;

      // Update portfolio summary
      await db('portfolios')
        .where({ id: portfolioId })
        .update({
          current_value: totalValue,
          total_invested: totalCost,
          total_pnl: totalPnl,
          total_pnl_percentage: totalPnlPercentage,
          last_updated: new Date()
        });

    } catch (error) {
      logger.errorWithContext(error, { portfolioId });
    }
  }

  async cleanupExpiredTokens() {
    try {
      const result = await db('blacklisted_tokens')
        .where('token_expires_at', '<', new Date())
        .del();

      logger.info(`Cleaned up ${result} expired tokens`);
    } catch (error) {
      logger.errorWithContext(error, { cronJob: 'cleanupExpiredTokens' });
    }
  }

  async sendDailyPortfolioSummaries() {
    try {
      // Get users who have enabled daily summaries
      const users = await db('users')
        .whereRaw("notification_settings->>'portfolio_updates' = 'true'")
        .where({ is_active: true, email_verified: true });

      for (const user of users) {
        await this.sendUserPortfolioSummary(user);
      }

      logger.info(`Sent daily portfolio summaries to ${users.length} users`);
    } catch (error) {
      logger.errorWithContext(error, { cronJob: 'sendDailyPortfolioSummaries' });
    }
  }

  async sendUserPortfolioSummary(user) {
    try {
      // Get user's portfolios with current values
      const portfolios = await db('portfolios')
        .where({ user_id: user.id, is_deleted: false });

      if (portfolios.length === 0) {
        return;
      }

      // Calculate total portfolio value and performance
      let totalValue = 0;
      let totalPnl = 0;

      portfolios.forEach(portfolio => {
        totalValue += portfolio.current_value || 0;
        totalPnl += portfolio.total_pnl || 0;
      });

      const emailData = {
        user,
        portfolios,
        totalValue,
        totalPnl,
        totalPnlPercentage: totalValue > 0 ? (totalPnl / (totalValue - totalPnl)) * 100 : 0
      };

      await this.emailService.sendPortfolioSummary(user.email, emailData);
    } catch (error) {
      logger.errorWithContext(error, { userId: user.id });
    }
  }

  async cleanupOldNotifications() {
    try {
      // Delete notifications older than 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const result = await db('notifications')
        .where('created_at', '<', thirtyDaysAgo)
        .where({ is_archived: true })
        .del();

      logger.info(`Cleaned up ${result} old notifications`);
    } catch (error) {
      logger.errorWithContext(error, { cronJob: 'cleanupOldNotifications' });
    }
  }

  async updateDefiPositions() {
    try {
      // Get all active DeFi positions
      const positions = await db('defi_positions')
        .where({ status: 'active', is_deleted: false });

      for (const position of positions) {
        await this.updateSingleDefiPosition(position);
      }

      logger.info(`Updated ${positions.length} DeFi positions`);
    } catch (error) {
      logger.errorWithContext(error, { cronJob: 'updateDefiPositions' });
    }
  }

  async updateSingleDefiPosition(position) {
    try {
      // This would integrate with various DeFi protocols
      // For now, just update the timestamp
      await db('defi_positions')
        .where({ id: position.id })
        .update({ last_updated: new Date() });
    } catch (error) {
      logger.errorWithContext(error, { positionId: position.id });
    }
  }

  async archiveOldAuditLogs() {
    try {
      // Archive audit logs older than 1 year
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

      const result = await db('audit_logs')
        .where('created_at', '<', oneYearAgo)
        .del();

      logger.info(`Archived ${result} old audit logs`);
    } catch (error) {
      logger.errorWithContext(error, { cronJob: 'archiveOldAuditLogs' });
    }
  }

  async getActiveSymbols() {
    try {
      const [portfolioSymbols, watchlistSymbols] = await Promise.all([
        db('portfolio_holdings')
          .distinct('symbol')
          .where({ is_deleted: false }),
        db('watchlist_items')
          .distinct('symbol')
          .where({ is_deleted: false })
      ]);

      const allSymbols = [
        ...portfolioSymbols.map(row => row.symbol),
        ...watchlistSymbols.map(row => row.symbol)
      ];

      return [...new Set(allSymbols)]; // Remove duplicates
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getActiveSymbols' });
      return [];
    }
  }

  stop() {
    if (!this.isRunning) {
      return;
    }

    this.jobs.forEach((job, name) => {
      job.stop();
      logger.info(`Stopped cron job: ${name}`);
    });

    this.jobs.clear();
    this.isRunning = false;
    
    logger.info('Cron service stopped');
  }

  getJobStatus() {
    const status = {};
    
    this.jobs.forEach((job, name) => {
      status[name] = {
        running: job.running,
        scheduled: job.scheduled
      };
    });

    return status;
  }
}

module.exports = CronService;
