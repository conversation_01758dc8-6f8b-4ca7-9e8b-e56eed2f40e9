const nodemailer = require('nodemailer');
const logger = require('../config/logger');

class EmailService {
  constructor() {
    this.transporter = null;
    this.initialize();
  }

  initialize() {
    try {
      // Configure email transporter based on environment
      if (process.env.NODE_ENV === 'production') {
        // Production configuration (e.g., SendGrid, AWS SES, etc.)
        this.transporter = nodemailer.createTransporter({
          service: process.env.EMAIL_SERVICE || 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASSWORD
          }
        });
      } else {
        // Development configuration (Ethereal Email for testing)
        this.transporter = nodemailer.createTransporter({
          host: 'smtp.ethereal.email',
          port: 587,
          auth: {
            user: process.env.EMAIL_USER || '<EMAIL>',
            pass: process.env.EMAIL_PASSWORD || 'ethereal.pass'
          }
        });
      }

      logger.info('Email service initialized');
    } catch (error) {
      logger.errorWithContext(error, { service: 'EmailService' });
    }
  }

  async sendEmail(to, subject, html, text = null) {
    try {
      if (!this.transporter) {
        throw new Error('Email transporter not initialized');
      }

      const mailOptions = {
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to,
        subject,
        html,
        text: text || this.stripHtml(html)
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info(`Email sent successfully to ${to}`, { messageId: result.messageId });
      return result;
    } catch (error) {
      logger.errorWithContext(error, { to, subject });
      throw error;
    }
  }

  async sendVerificationEmail(email, token, firstName) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify-email?token=${token}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Verify Your Email - CryptoSphere</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to CryptoSphere!</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName},</h2>
            <p>Thank you for signing up for CryptoSphere! To complete your registration, please verify your email address by clicking the button below:</p>
            
            <div style="text-align: center;">
              <a href="${verificationUrl}" class="button">Verify Email Address</a>
            </div>
            
            <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #667eea;">${verificationUrl}</p>
            
            <p>This verification link will expire in 24 hours for security reasons.</p>
            
            <p>If you didn't create an account with CryptoSphere, please ignore this email.</p>
            
            <p>Best regards,<br>The CryptoSphere Team</p>
          </div>
          <div class="footer">
            <p>© 2024 CryptoSphere. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendEmail(email, 'Verify Your Email - CryptoSphere', html);
  }

  async sendPasswordResetEmail(email, token, firstName) {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/reset-password?token=${token}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Reset Your Password - CryptoSphere</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Password Reset Request</h1>
          </div>
          <div class="content">
            <h2>Hi ${firstName},</h2>
            <p>We received a request to reset your password for your CryptoSphere account.</p>
            
            <div style="text-align: center;">
              <a href="${resetUrl}" class="button">Reset Password</a>
            </div>
            
            <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #667eea;">${resetUrl}</p>
            
            <div class="warning">
              <strong>Security Notice:</strong> This password reset link will expire in 1 hour for security reasons. If you didn't request this password reset, please ignore this email and your password will remain unchanged.
            </div>
            
            <p>For your security, we recommend:</p>
            <ul>
              <li>Using a strong, unique password</li>
              <li>Enabling two-factor authentication</li>
              <li>Never sharing your login credentials</li>
            </ul>
            
            <p>Best regards,<br>The CryptoSphere Team</p>
          </div>
          <div class="footer">
            <p>© 2024 CryptoSphere. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendEmail(email, 'Reset Your Password - CryptoSphere', html);
  }

  async sendPortfolioSummary(email, data) {
    const { user, portfolios, totalValue, totalPnl, totalPnlPercentage } = data;
    
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    };

    const formatPercentage = (percentage) => {
      return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(2)}%`;
    };

    const portfolioRows = portfolios.map(portfolio => `
      <tr>
        <td style="padding: 10px; border-bottom: 1px solid #eee;">${portfolio.name}</td>
        <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right;">${formatCurrency(portfolio.current_value || 0)}</td>
        <td style="padding: 10px; border-bottom: 1px solid #eee; text-align: right; color: ${(portfolio.total_pnl || 0) >= 0 ? '#28a745' : '#dc3545'};">
          ${formatCurrency(portfolio.total_pnl || 0)} (${formatPercentage(portfolio.total_pnl_percentage || 0)})
        </td>
      </tr>
    `).join('');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Daily Portfolio Summary - CryptoSphere</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .summary-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
          .metric { display: inline-block; margin: 10px 20px; text-align: center; }
          .metric-value { font-size: 24px; font-weight: bold; }
          .metric-label { font-size: 14px; color: #666; }
          .positive { color: #28a745; }
          .negative { color: #dc3545; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th { background: #f8f9fa; padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Daily Portfolio Summary</h1>
            <p>${new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
          </div>
          <div class="content">
            <h2>Hi ${user.first_name},</h2>
            <p>Here's your daily portfolio summary:</p>
            
            <div class="summary-card">
              <div class="metric">
                <div class="metric-value">${formatCurrency(totalValue)}</div>
                <div class="metric-label">Total Portfolio Value</div>
              </div>
              <div class="metric">
                <div class="metric-value ${totalPnl >= 0 ? 'positive' : 'negative'}">${formatCurrency(totalPnl)}</div>
                <div class="metric-label">Total P&L</div>
              </div>
              <div class="metric">
                <div class="metric-value ${totalPnlPercentage >= 0 ? 'positive' : 'negative'}">${formatPercentage(totalPnlPercentage)}</div>
                <div class="metric-label">Total Return</div>
              </div>
            </div>

            <h3>Portfolio Breakdown</h3>
            <table>
              <thead>
                <tr>
                  <th>Portfolio</th>
                  <th style="text-align: right;">Current Value</th>
                  <th style="text-align: right;">P&L</th>
                </tr>
              </thead>
              <tbody>
                ${portfolioRows}
              </tbody>
            </table>

            <p style="text-align: center; margin-top: 30px;">
              <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/dashboard" style="display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">
                View Full Dashboard
              </a>
            </p>

            <p style="font-size: 14px; color: #666; margin-top: 30px;">
              You're receiving this email because you have daily portfolio summaries enabled. 
              You can change your notification preferences in your account settings.
            </p>
          </div>
          <div class="footer">
            <p>© 2024 CryptoSphere. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendEmail(email, 'Daily Portfolio Summary - CryptoSphere', html);
  }

  async sendPriceAlert(email, alertData) {
    const { symbol, condition, targetValue, currentPrice, alertName } = alertData;
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Price Alert Triggered - CryptoSphere</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .alert-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #ffc107; }
          .price-display { font-size: 24px; font-weight: bold; color: #667eea; text-align: center; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚨 Price Alert Triggered</h1>
          </div>
          <div class="content">
            <div class="alert-card">
              <h2>${alertName || `${symbol} Price Alert`}</h2>
              <p><strong>${symbol}</strong> has ${condition} your target price of <strong>$${targetValue}</strong></p>
              
              <div class="price-display">
                Current Price: $${currentPrice}
              </div>
              
              <p style="text-align: center;">
                <a href="${process.env.FRONTEND_URL || 'http://localhost:3000'}/alerts" style="display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">
                  Manage Alerts
                </a>
              </p>
            </div>
            
            <p style="font-size: 14px; color: #666;">
              This alert was triggered at ${new Date().toLocaleString()}. 
              You can manage your price alerts in your CryptoSphere dashboard.
            </p>
          </div>
          <div class="footer">
            <p>© 2024 CryptoSphere. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return await this.sendEmail(email, `Price Alert: ${symbol} - CryptoSphere`, html);
  }

  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '');
  }

  async testConnection() {
    try {
      if (!this.transporter) {
        throw new Error('Email transporter not initialized');
      }

      await this.transporter.verify();
      logger.info('Email service connection test successful');
      return true;
    } catch (error) {
      logger.errorWithContext(error, { service: 'EmailService', operation: 'testConnection' });
      return false;
    }
  }
}

module.exports = EmailService;
