const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const { v4: uuidv4 } = require('uuid');
const passport = require('passport');
const { body, validationResult } = require('express-validator');

const db = require('../config/database');
const redis = require('../config/redis');
const logger = require('../config/logger');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { authRateLimit } = require('../middleware/auth');
const EmailService = require('../services/email');

const router = express.Router();
const emailService = new EmailService();

// Validation rules
const registerValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('username').isLength({ min: 3, max: 30 }).withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/).withMessage('Username can only contain letters, numbers, and underscores'),
  body('firstName').isLength({ min: 1, max: 50 }).withMessage('First name is required and must be less than 50 characters'),
  body('lastName').isLength({ min: 1, max: 50 }).withMessage('Last name is required and must be less than 50 characters')
];

const loginValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').notEmpty().withMessage('Password is required')
];

// Generate JWT token
const generateToken = (user, jti = null) => {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role,
    jti: jti || uuidv4()
  };

  return jwt.sign(payload, process.env.JWT_SECRET || 'your-secret-key', {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    issuer: 'cryptosphere',
    audience: 'cryptosphere-users'
  });
};

// Register new user
router.post('/register', authRateLimit, registerValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const { email, password, username, firstName, lastName } = req.body;

  // Check if user already exists
  const existingUser = await db('users')
    .where({ email: email.toLowerCase() })
    .orWhere({ username })
    .first();

  if (existingUser) {
    const field = existingUser.email === email.toLowerCase() ? 'email' : 'username';
    return res.status(409).json({
      error: 'User already exists',
      message: `A user with this ${field} already exists`
    });
  }

  // Hash password
  const saltRounds = 12;
  const passwordHash = await bcrypt.hash(password, saltRounds);

  // Generate email verification token
  const emailVerificationToken = uuidv4();

  // Create user
  const [userId] = await db('users').insert({
    email: email.toLowerCase(),
    username,
    password_hash: passwordHash,
    first_name: firstName,
    last_name: lastName,
    email_verification_token: emailVerificationToken,
    created_at: new Date(),
    updated_at: new Date()
  }).returning('id');

  // Send verification email
  try {
    await emailService.sendVerificationEmail(email, emailVerificationToken, firstName);
  } catch (error) {
    logger.errorWithContext(error, { operation: 'sendVerificationEmail', email });
    // Don't fail registration if email fails
  }

  logger.business('User registered', { userId: userId.id, email, username });

  res.status(201).json({
    message: 'Registration successful',
    data: {
      message: 'Please check your email to verify your account',
      email: email.toLowerCase()
    }
  });
}));

// Login user
router.post('/login', authRateLimit, loginValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  passport.authenticate('local', { session: false }, async (err, user, info) => {
    if (err) {
      logger.errorWithContext(err, { operation: 'login', email: req.body.email });
      return res.status(500).json({
        error: 'Authentication error',
        message: 'Internal server error during login'
      });
    }

    if (!user) {
      return res.status(401).json({
        error: 'Authentication failed',
        message: info?.message || 'Invalid credentials'
      });
    }

    // Generate JWT token
    const jti = uuidv4();
    const token = generateToken(user, jti);

    // Store session in Redis
    const sessionData = {
      userId: user.id,
      email: user.email,
      loginTime: new Date().toISOString(),
      jti
    };

    await redis.setex(`session:${user.id}`, 24 * 60 * 60, sessionData); // 24 hours

    // Update last login
    await db('users')
      .where({ id: user.id })
      .update({ last_login: new Date() });

    res.json({
      message: 'Login successful',
      data: {
        token,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.first_name,
          lastName: user.last_name,
          role: user.role,
          emailVerified: user.email_verified,
          twoFactorEnabled: user.two_factor_enabled
        }
      }
    });
  })(req, res);
}));

// Verify email
router.post('/verify-email', catchAsync(async (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({
      error: 'Token required',
      message: 'Email verification token is required'
    });
  }

  const user = await db('users')
    .where({ email_verification_token: token })
    .first();

  if (!user) {
    return res.status(400).json({
      error: 'Invalid token',
      message: 'Invalid or expired verification token'
    });
  }

  // Update user as verified
  await db('users')
    .where({ id: user.id })
    .update({
      email_verified: true,
      email_verification_token: null,
      updated_at: new Date()
    });

  logger.business('Email verified', { userId: user.id, email: user.email });

  res.json({
    message: 'Email verified successfully',
    data: {
      message: 'Your email has been verified. You can now access all features.'
    }
  });
}));

// Resend verification email
router.post('/resend-verification', authRateLimit, catchAsync(async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      error: 'Email required',
      message: 'Email address is required'
    });
  }

  const user = await db('users')
    .where({ email: email.toLowerCase() })
    .first();

  if (!user) {
    // Don't reveal if email exists
    return res.json({
      message: 'If the email exists, a verification email has been sent'
    });
  }

  if (user.email_verified) {
    return res.status(400).json({
      error: 'Already verified',
      message: 'Email is already verified'
    });
  }

  // Generate new verification token
  const emailVerificationToken = uuidv4();
  
  await db('users')
    .where({ id: user.id })
    .update({
      email_verification_token: emailVerificationToken,
      updated_at: new Date()
    });

  // Send verification email
  try {
    await emailService.sendVerificationEmail(user.email, emailVerificationToken, user.first_name);
  } catch (error) {
    logger.errorWithContext(error, { operation: 'resendVerificationEmail', email: user.email });
    return res.status(500).json({
      error: 'Email service error',
      message: 'Failed to send verification email'
    });
  }

  res.json({
    message: 'Verification email sent',
    data: {
      message: 'Please check your email for the verification link'
    }
  });
}));

// Forgot password
router.post('/forgot-password', authRateLimit, catchAsync(async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      error: 'Email required',
      message: 'Email address is required'
    });
  }

  const user = await db('users')
    .where({ email: email.toLowerCase() })
    .first();

  if (!user) {
    // Don't reveal if email exists
    return res.json({
      message: 'If the email exists, a password reset link has been sent'
    });
  }

  // Generate reset token
  const resetToken = uuidv4();
  const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

  await db('users')
    .where({ id: user.id })
    .update({
      password_reset_token: resetToken,
      password_reset_expires: resetExpires,
      updated_at: new Date()
    });

  // Send reset email
  try {
    await emailService.sendPasswordResetEmail(user.email, resetToken, user.first_name);
  } catch (error) {
    logger.errorWithContext(error, { operation: 'sendPasswordResetEmail', email: user.email });
    return res.status(500).json({
      error: 'Email service error',
      message: 'Failed to send password reset email'
    });
  }

  res.json({
    message: 'Password reset email sent',
    data: {
      message: 'Please check your email for the password reset link'
    }
  });
}));

// Reset password
router.post('/reset-password', authRateLimit, [
  body('token').notEmpty().withMessage('Reset token is required'),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const { token, password } = req.body;

  const user = await db('users')
    .where({ password_reset_token: token })
    .where('password_reset_expires', '>', new Date())
    .first();

  if (!user) {
    return res.status(400).json({
      error: 'Invalid token',
      message: 'Invalid or expired reset token'
    });
  }

  // Hash new password
  const saltRounds = 12;
  const passwordHash = await bcrypt.hash(password, saltRounds);

  // Update password and clear reset token
  await db('users')
    .where({ id: user.id })
    .update({
      password_hash: passwordHash,
      password_reset_token: null,
      password_reset_expires: null,
      failed_login_attempts: 0,
      locked_until: null,
      updated_at: new Date()
    });

  logger.business('Password reset', { userId: user.id, email: user.email });

  res.json({
    message: 'Password reset successful',
    data: {
      message: 'Your password has been reset successfully. You can now log in with your new password.'
    }
  });
}));

// Logout user
router.post('/logout', catchAsync(async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (token) {
    try {
      const decoded = jwt.decode(token);

      if (decoded?.jti) {
        // Add token to blacklist
        await db('blacklisted_tokens').insert({
          token: decoded.jti,
          expires_at: new Date(decoded.exp * 1000),
          created_at: new Date()
        });

        // Remove session from Redis
        if (decoded.id) {
          await redis.del(`session:${decoded.id}`);
        }
      }
    } catch (error) {
      logger.errorWithContext(error, { operation: 'logout' });
    }
  }

  res.json({
    message: 'Logout successful'
  });
}));

module.exports = router;
