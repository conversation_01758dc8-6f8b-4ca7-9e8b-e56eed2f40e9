/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('defi_positions', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign keys
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    table.uuid('portfolio_id').nullable();
    table.foreign('portfolio_id').references('id').inTable('portfolios').onDelete('SET NULL');
    
    // Protocol information
    table.string('protocol', 100).notNullable(); // Uniswap, Aave, Compound, etc.
    table.string('protocol_version', 20).nullable(); // v2, v3, etc.
    table.string('network', 50).notNullable(); // ethereum, polygon, arbitrum, etc.
    table.string('contract_address', 100).nullable();
    table.string('position_id', 255).nullable(); // Protocol-specific position ID
    
    // Position details
    table.enum('type', ['lending', 'borrowing', 'liquidity_pool', 'staking', 'farming', 'vault', 'options', 'futures']).notNullable();
    table.enum('status', ['active', 'closed', 'liquidated', 'expired']).defaultTo('active');
    
    // Asset information
    table.string('primary_token', 20).notNullable();
    table.string('secondary_token', 20).nullable(); // For LP positions
    table.decimal('primary_amount', 30, 18).notNullable();
    table.decimal('secondary_amount', 30, 18).nullable();
    
    // Financial metrics
    table.decimal('initial_value_usd', 20, 8).nullable();
    table.decimal('current_value_usd', 20, 8).nullable();
    table.decimal('pnl_usd', 20, 8).nullable();
    table.decimal('pnl_percentage', 10, 4).nullable();
    
    // Yield/rewards information
    table.decimal('total_rewards_earned', 20, 8).defaultTo(0);
    table.decimal('current_apy', 10, 4).nullable();
    table.decimal('average_apy', 10, 4).nullable();
    table.string('reward_token', 20).nullable();
    table.decimal('unclaimed_rewards', 20, 8).defaultTo(0);
    
    // Liquidity pool specific
    table.decimal('impermanent_loss', 20, 8).nullable();
    table.decimal('fees_earned', 20, 8).defaultTo(0);
    table.decimal('pool_share_percentage', 10, 8).nullable();
    
    // Lending/borrowing specific
    table.decimal('collateral_amount', 30, 18).nullable();
    table.decimal('borrowed_amount', 30, 18).nullable();
    table.decimal('health_factor', 10, 4).nullable();
    table.decimal('liquidation_threshold', 10, 4).nullable();
    table.decimal('interest_rate', 10, 4).nullable();
    
    // Transaction tracking
    table.string('entry_tx_hash', 100).nullable();
    table.string('exit_tx_hash', 100).nullable();
    table.timestamp('entered_at').nullable();
    table.timestamp('exited_at').nullable();
    table.decimal('gas_fees_paid', 20, 8).defaultTo(0);
    
    // Risk metrics
    table.enum('risk_level', ['low', 'medium', 'high', 'extreme']).nullable();
    table.json('risk_factors').nullable();
    
    // Metadata
    table.json('metadata').nullable();
    table.json('tags').nullable();
    table.text('notes').nullable();
    table.timestamp('last_updated').nullable();
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['portfolio_id']);
    table.index(['protocol']);
    table.index(['network']);
    table.index(['type']);
    table.index(['status']);
    table.index(['primary_token']);
    table.index(['entered_at']);
    table.index(['is_deleted']);
    table.index(['user_id', 'status']);
    table.index(['protocol', 'network']);
    table.index(['position_id']);
    table.index(['entry_tx_hash']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('defi_positions');
};
