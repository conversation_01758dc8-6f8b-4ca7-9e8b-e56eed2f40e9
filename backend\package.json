{"name": "cryptosphere-backend", "version": "1.0.0", "description": "CryptoSphere Backend API - Advanced Cryptocurrency Portfolio Management Platform", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest --watchAll=false", "test:watch": "jest --watchAll", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "db:migrate": "knex migrate:latest", "db:rollback": "knex migrate:rollback", "db:seed": "knex seed:run", "db:reset": "npm run db:rollback && npm run db:migrate && npm run db:seed", "build": "babel src -d dist", "start:prod": "NODE_ENV=production node dist/app.js"}, "keywords": ["cryptocurrency", "portfolio", "blockchain", "defi", "trading", "analytics"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "knex": "^3.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "socket.io": "^4.7.4", "axios": "^1.6.2", "node-cron": "^3.0.3", "winston": "^3.11.0", "joi": "^17.11.0", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "crypto": "^1.0.1", "speakeasy": "^2.0.0", "qrcode": "^1.5.3", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "csv-parser": "^3.0.0", "pdf-lib": "^1.17.1", "web3": "^4.3.0", "ethers": "^6.8.1"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/CryptoSphere.git"}, "bugs": {"url": "https://github.com/HectorTa1989/CryptoSphere/issues"}, "homepage": "https://github.com/HectorTa1989/CryptoSphere#readme"}