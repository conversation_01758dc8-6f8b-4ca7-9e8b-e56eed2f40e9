const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');

const db = require('../config/database');
const logger = require('../config/logger');
const { catchAsync } = require('../middleware/errorHandler');

const router = express.Router();

// Validation middleware
const createPostValidation = [
  body('content').isLength({ min: 1, max: 2000 }).trim(),
  body('type').optional().isIn(['text', 'portfolio_share', 'trade_share', 'analysis', 'news_share', 'poll']),
  body('visibility').optional().isIn(['public', 'followers', 'private']),
  body('allow_comments').optional().isBoolean(),
  body('allow_shares').optional().isBoolean(),
  body('media_urls').optional().isArray(),
  body('attachments').optional().isObject(),
  body('hashtags').optional().isArray(),
  body('symbols_mentioned').optional().isArray(),
];

const createCommentValidation = [
  body('content').isLength({ min: 1, max: 1000 }).trim(),
  body('parent_comment_id').optional().isUUID(),
];

// Get social feed
router.get('/feed', catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 20, 
    type = 'all',
    following_only = false 
  } = req.query;
  
  const offset = (page - 1) * limit;

  let query = db('social_posts as sp')
    .select([
      'sp.*',
      'u.username',
      'u.first_name',
      'u.last_name',
      'u.avatar_url',
      'u.role'
    ])
    .join('users as u', 'sp.user_id', 'u.id')
    .where({ 'sp.is_deleted': false, 'sp.is_approved': true })
    .limit(limit)
    .offset(offset)
    .orderBy('sp.created_at', 'desc');

  // Filter by visibility
  if (following_only) {
    // Get user's following list
    const following = await db('social_follows')
      .select('following_id')
      .where({ follower_id: req.user.id, is_active: true });
    
    const followingIds = following.map(f => f.following_id);
    followingIds.push(req.user.id); // Include user's own posts
    
    query = query.whereIn('sp.user_id', followingIds);
  } else {
    // Show public posts and posts from users the current user follows
    query = query.where(function() {
      this.where('sp.visibility', 'public')
        .orWhere(function() {
          this.where('sp.visibility', 'followers')
            .whereIn('sp.user_id', function() {
              this.select('following_id')
                .from('social_follows')
                .where({ follower_id: req.user.id, is_active: true });
            });
        })
        .orWhere('sp.user_id', req.user.id); // User's own posts
    });
  }

  if (type !== 'all') {
    query = query.where({ 'sp.type': type });
  }

  const posts = await query;

  // Parse JSON fields and add interaction status
  const postsWithInteractions = await Promise.all(
    posts.map(async (post) => {
      // Check if user has liked this post
      const userLike = await db('social_interactions')
        .where({ 
          user_id: req.user.id, 
          post_id: post.id, 
          type: 'like',
          is_active: true 
        })
        .first();

      // Check if user has bookmarked this post
      const userBookmark = await db('social_interactions')
        .where({ 
          user_id: req.user.id, 
          post_id: post.id, 
          type: 'bookmark',
          is_active: true 
        })
        .first();

      return {
        ...post,
        media_urls: post.media_urls ? JSON.parse(post.media_urls) : [],
        attachments: post.attachments ? JSON.parse(post.attachments) : null,
        hashtags: post.hashtags ? JSON.parse(post.hashtags) : [],
        mentions: post.mentions ? JSON.parse(post.mentions) : [],
        symbols_mentioned: post.symbols_mentioned ? JSON.parse(post.symbols_mentioned) : [],
        user_liked: !!userLike,
        user_bookmarked: !!userBookmark,
        is_own_post: post.user_id === req.user.id
      };
    })
  );

  res.json({
    message: 'Social feed retrieved successfully',
    data: {
      posts: postsWithInteractions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        has_more: posts.length === limit
      }
    }
  });
}));

// Create new post
router.post('/posts', createPostValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const {
    content,
    type = 'text',
    visibility = 'public',
    allow_comments = true,
    allow_shares = true,
    media_urls = [],
    attachments = null,
    hashtags = [],
    mentions = [],
    symbols_mentioned = []
  } = req.body;

  const postId = uuidv4();
  const post = {
    id: postId,
    user_id: req.user.id,
    content,
    type,
    visibility,
    allow_comments,
    allow_shares,
    media_urls: JSON.stringify(media_urls),
    attachments: attachments ? JSON.stringify(attachments) : null,
    hashtags: JSON.stringify(hashtags),
    mentions: JSON.stringify(mentions),
    symbols_mentioned: JSON.stringify(symbols_mentioned),
    created_at: new Date(),
    updated_at: new Date()
  };

  await db('social_posts').insert(post);

  // Create notifications for mentioned users
  if (mentions.length > 0) {
    const NotificationService = require('../services/notification');
    const notificationService = new NotificationService();

    for (const mentionedUserId of mentions) {
      await notificationService.createNotification({
        user_id: mentionedUserId,
        title: 'You were mentioned in a post',
        message: `${req.user.username} mentioned you in a post`,
        type: 'social_interaction',
        channels: ['in_app', 'push'],
        related_entity_type: 'post',
        related_entity_id: postId,
        action_url: `/social/posts/${postId}`
      });
    }
  }

  logger.business('Social post created', { userId: req.user.id, postId, type });

  res.status(201).json({
    message: 'Post created successfully',
    data: {
      ...post,
      media_urls: JSON.parse(post.media_urls),
      attachments: post.attachments ? JSON.parse(post.attachments) : null,
      hashtags: JSON.parse(post.hashtags),
      mentions: JSON.parse(post.mentions),
      symbols_mentioned: JSON.parse(post.symbols_mentioned)
    }
  });
}));

// Like/unlike post
router.post('/posts/:id/like', catchAsync(async (req, res) => {
  const postId = req.params.id;

  // Check if post exists and is accessible
  const post = await db('social_posts')
    .where({ id: postId, is_deleted: false })
    .first();

  if (!post) {
    return res.status(404).json({
      error: 'Post not found',
      message: 'Post not found or has been deleted'
    });
  }

  // Check if user already liked this post
  const existingLike = await db('social_interactions')
    .where({ 
      user_id: req.user.id, 
      post_id: postId, 
      type: 'like' 
    })
    .first();

  if (existingLike) {
    if (existingLike.is_active) {
      // Unlike
      await db('social_interactions')
        .where({ id: existingLike.id })
        .update({ is_active: false, updated_at: new Date() });
      
      await db('social_posts')
        .where({ id: postId })
        .decrement('likes_count', 1);

      res.json({
        message: 'Post unliked successfully',
        data: { liked: false }
      });
    } else {
      // Re-like
      await db('social_interactions')
        .where({ id: existingLike.id })
        .update({ is_active: true, updated_at: new Date() });
      
      await db('social_posts')
        .where({ id: postId })
        .increment('likes_count', 1);

      res.json({
        message: 'Post liked successfully',
        data: { liked: true }
      });
    }
  } else {
    // New like
    await db('social_interactions').insert({
      id: uuidv4(),
      user_id: req.user.id,
      post_id: postId,
      type: 'like',
      is_active: true,
      created_at: new Date(),
      updated_at: new Date()
    });

    await db('social_posts')
      .where({ id: postId })
      .increment('likes_count', 1);

    // Notify post owner
    if (post.user_id !== req.user.id) {
      const NotificationService = require('../services/notification');
      const notificationService = new NotificationService();

      await notificationService.createNotification({
        user_id: post.user_id,
        title: 'Someone liked your post',
        message: `${req.user.username} liked your post`,
        type: 'social_interaction',
        channels: ['in_app'],
        related_entity_type: 'post',
        related_entity_id: postId,
        action_url: `/social/posts/${postId}`
      });
    }

    res.json({
      message: 'Post liked successfully',
      data: { liked: true }
    });
  }
}));

// Add comment to post
router.post('/posts/:id/comments', createCommentValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const postId = req.params.id;
  const { content, parent_comment_id } = req.body;

  // Check if post exists and allows comments
  const post = await db('social_posts')
    .where({ id: postId, is_deleted: false })
    .first();

  if (!post) {
    return res.status(404).json({
      error: 'Post not found',
      message: 'Post not found or has been deleted'
    });
  }

  if (!post.allow_comments) {
    return res.status(403).json({
      error: 'Comments disabled',
      message: 'Comments are disabled for this post'
    });
  }

  const commentId = uuidv4();
  const comment = {
    id: commentId,
    user_id: req.user.id,
    post_id: postId,
    type: 'comment',
    content,
    parent_comment_id: parent_comment_id || null,
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  };

  await db('social_interactions').insert(comment);

  // Update comment count
  await db('social_posts')
    .where({ id: postId })
    .increment('comments_count', 1);

  // Notify post owner
  if (post.user_id !== req.user.id) {
    const NotificationService = require('../services/notification');
    const notificationService = new NotificationService();

    await notificationService.createNotification({
      user_id: post.user_id,
      title: 'New comment on your post',
      message: `${req.user.username} commented on your post`,
      type: 'social_interaction',
      channels: ['in_app', 'push'],
      related_entity_type: 'post',
      related_entity_id: postId,
      action_url: `/social/posts/${postId}`
    });
  }

  logger.business('Comment created', { userId: req.user.id, postId, commentId });

  res.status(201).json({
    message: 'Comment added successfully',
    data: comment
  });
}));

module.exports = router;
