/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('market_data', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Asset information
    table.string('symbol', 20).notNullable();
    table.string('name', 100).nullable();
    table.string('coingecko_id', 100).nullable();
    table.string('coinmarketcap_id', 100).nullable();
    table.string('contract_address', 100).nullable();
    table.string('network', 50).nullable();
    
    // Price data
    table.decimal('price_usd', 20, 8).notNullable();
    table.decimal('price_btc', 20, 8).nullable();
    table.decimal('price_eth', 20, 8).nullable();
    
    // Market metrics
    table.decimal('market_cap', 20, 2).nullable();
    table.decimal('fully_diluted_valuation', 20, 2).nullable();
    table.decimal('volume_24h', 20, 2).nullable();
    table.decimal('volume_change_24h', 10, 4).nullable();
    table.bigInteger('circulating_supply').nullable();
    table.bigInteger('total_supply').nullable();
    table.bigInteger('max_supply').nullable();
    
    // Price changes
    table.decimal('price_change_1h', 10, 4).nullable();
    table.decimal('price_change_24h', 10, 4).nullable();
    table.decimal('price_change_7d', 10, 4).nullable();
    table.decimal('price_change_30d', 10, 4).nullable();
    table.decimal('price_change_1y', 10, 4).nullable();
    
    // Historical highs/lows
    table.decimal('ath_price', 20, 8).nullable();
    table.timestamp('ath_date').nullable();
    table.decimal('ath_change_percentage', 10, 4).nullable();
    table.decimal('atl_price', 20, 8).nullable();
    table.timestamp('atl_date').nullable();
    table.decimal('atl_change_percentage', 10, 4).nullable();
    
    // Market rankings
    table.integer('market_cap_rank').nullable();
    table.integer('volume_rank').nullable();
    
    // Technical indicators
    table.decimal('rsi_14', 10, 4).nullable();
    table.decimal('ma_20', 20, 8).nullable();
    table.decimal('ma_50', 20, 8).nullable();
    table.decimal('ma_200', 20, 8).nullable();
    table.decimal('volatility_30d', 10, 4).nullable();
    
    // Social metrics
    table.integer('twitter_followers').nullable();
    table.integer('reddit_subscribers').nullable();
    table.integer('github_stars').nullable();
    table.integer('github_forks').nullable();
    table.decimal('social_sentiment', 10, 4).nullable();
    
    // Developer activity
    table.integer('github_commits_4w').nullable();
    table.integer('github_contributors').nullable();
    table.timestamp('last_updated').nullable();
    
    // Data source tracking
    table.string('data_source', 50).defaultTo('coingecko');
    table.timestamp('fetched_at').defaultTo(knex.fn.now());
    table.json('raw_data').nullable(); // Store original API response
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['symbol']);
    table.index(['market_cap_rank']);
    table.index(['volume_rank']);
    table.index(['fetched_at']);
    table.index(['last_updated']);
    table.index(['coingecko_id']);
    table.index(['symbol', 'fetched_at']);
    
    // Unique constraint for symbol + timestamp (for historical data)
    table.unique(['symbol', 'fetched_at'], 'unique_symbol_timestamp');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('market_data');
};
