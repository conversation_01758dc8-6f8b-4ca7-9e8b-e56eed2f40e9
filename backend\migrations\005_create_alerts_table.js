/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('alerts', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to users
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Alert configuration
    table.string('name', 100).notNullable();
    table.text('description').nullable();
    table.string('symbol', 20).notNullable();
    table.enum('type', ['price', 'percentage', 'volume', 'market_cap', 'portfolio_value', 'portfolio_percentage']).notNullable();
    table.enum('condition', ['above', 'below', 'crosses_above', 'crosses_below', 'equals']).notNullable();
    table.decimal('target_value', 20, 8).notNullable();
    table.string('base_currency', 10).defaultTo('USD');
    
    // Notification settings
    table.json('notification_methods').notNullable(); // ['email', 'push', 'sms', 'webhook']
    table.string('webhook_url', 500).nullable();
    table.json('webhook_headers').nullable();
    table.boolean('is_recurring').defaultTo(false);
    table.integer('cooldown_minutes').defaultTo(60);
    
    // Status and tracking
    table.boolean('is_active').defaultTo(true);
    table.boolean('is_triggered').defaultTo(false);
    table.timestamp('last_triggered').nullable();
    table.timestamp('last_checked').nullable();
    table.integer('trigger_count').defaultTo(0);
    table.decimal('current_value', 20, 8).nullable();
    table.decimal('trigger_value', 20, 8).nullable();
    
    // Advanced settings
    table.integer('max_triggers').nullable();
    table.timestamp('expires_at').nullable();
    table.json('conditions').nullable(); // For complex multi-condition alerts
    table.string('time_frame', 20).defaultTo('1m'); // 1m, 5m, 15m, 1h, 4h, 1d
    
    // Metadata
    table.json('metadata').nullable();
    table.json('tags').nullable();
    table.text('notes').nullable();
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['symbol']);
    table.index(['type']);
    table.index(['is_active']);
    table.index(['is_triggered']);
    table.index(['last_triggered']);
    table.index(['expires_at']);
    table.index(['created_at']);
    table.index(['is_deleted']);
    table.index(['user_id', 'is_active']);
    table.index(['symbol', 'is_active']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('alerts');
};
