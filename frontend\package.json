{"name": "cryptosphere-frontend", "version": "1.0.0", "description": "CryptoSphere Frontend - Advanced Cryptocurrency Portfolio Management Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest --watchAll=false", "test:watch": "jest --watchAll", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build", "export": "next export"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "zustand": "^4.4.7", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-query": "^3.39.3", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "chart.js": "^4.4.1", "react-chartjs-2": "^5.2.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "react-virtual": "^2.10.4", "react-window": "^1.8.8", "@types/react-window": "^1.8.8", "react-intersection-observer": "^9.5.3", "react-use": "^17.4.2", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "numeral": "^2.0.6", "@types/numeral": "^2.0.5", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.294.0", "react-dropzone": "^14.2.3", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "@types/react-datepicker": "^4.19.4", "react-helmet-async": "^2.0.4", "next-themes": "^0.2.1", "next-pwa": "^5.6.0", "workbox-webpack-plugin": "^7.0.0"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "@types/jest": "^29.5.8", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.0", "msw": "^2.0.11"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test"}}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/CryptoSphere.git"}, "bugs": {"url": "https://github.com/HectorTa1989/CryptoSphere/issues"}, "homepage": "https://github.com/HectorTa1989/CryptoSphere#readme"}