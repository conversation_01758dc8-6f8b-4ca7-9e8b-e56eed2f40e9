/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('watchlist_items', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to watchlists
    table.uuid('watchlist_id').notNullable();
    table.foreign('watchlist_id').references('id').inTable('watchlists').onDelete('CASCADE');
    
    // Asset information
    table.string('symbol', 20).notNullable();
    table.string('name', 100).nullable();
    table.string('asset_type', 50).defaultTo('cryptocurrency');
    table.string('network', 50).nullable();
    table.string('contract_address', 100).nullable();
    
    // Display settings
    table.integer('sort_order').defaultTo(0);
    table.text('notes').nullable();
    table.json('custom_fields').nullable();
    
    // Price tracking
    table.decimal('target_price', 20, 8).nullable();
    table.decimal('stop_loss', 20, 8).nullable();
    table.decimal('take_profit', 20, 8).nullable();
    table.decimal('entry_price', 20, 8).nullable();
    table.timestamp('price_alert_last_triggered').nullable();
    
    // Metadata
    table.json('metadata').nullable();
    table.json('tags').nullable();
    table.timestamp('added_at').defaultTo(knex.fn.now());
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['watchlist_id']);
    table.index(['symbol']);
    table.index(['asset_type']);
    table.index(['sort_order']);
    table.index(['added_at']);
    table.index(['is_deleted']);
    table.index(['watchlist_id', 'symbol']);
    table.index(['watchlist_id', 'sort_order']);
    
    // Unique constraint for watchlist + symbol combination
    table.unique(['watchlist_id', 'symbol'], 'unique_watchlist_symbol');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('watchlist_items');
};
