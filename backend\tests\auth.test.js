const request = require('supertest');
const app = require('../src/app');
const db = require('../src/config/database');
const redis = require('../src/config/redis');

describe('Authentication Endpoints', () => {
  let server;

  beforeAll(async () => {
    // Setup test database
    await db.migrate.latest();
    server = app.listen(0);
  });

  afterAll(async () => {
    // Cleanup
    await db.migrate.rollback();
    await db.destroy();
    await redis.client.quit();
    await server.close();
  });

  beforeEach(async () => {
    // Clean up database before each test
    await db('users').del();
    await db('blacklisted_tokens').del();
  });

  describe('POST /api/auth/register', () => {
    const validUserData = {
      email: '<EMAIL>',
      password: 'Test123!@#',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User'
    };

    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(validUserData)
        .expect(201);

      expect(response.body).toHaveProperty('message', 'Registration successful');
      expect(response.body.data).toHaveProperty('message');
      expect(response.body.data).toHaveProperty('email', validUserData.email);

      // Verify user was created in database
      const user = await db('users').where({ email: validUserData.email }).first();
      expect(user).toBeTruthy();
      expect(user.username).toBe(validUserData.username);
      expect(user.email_verified).toBe(false);
    });

    it('should return 400 for invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...validUserData,
          email: 'invalid-email'
        })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Please provide a valid email'
          })
        ])
      );
    });

    it('should return 400 for weak password', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...validUserData,
          password: 'weak'
        })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: expect.stringContaining('Password must contain')
          })
        ])
      );
    });

    it('should return 409 for duplicate email', async () => {
      // Create user first
      await request(app)
        .post('/api/auth/register')
        .send(validUserData)
        .expect(201);

      // Try to register with same email
      const response = await request(app)
        .post('/api/auth/register')
        .send(validUserData)
        .expect(409);

      expect(response.body).toHaveProperty('error', 'User already exists');
      expect(response.body.message).toContain('email');
    });

    it('should return 409 for duplicate username', async () => {
      // Create user first
      await request(app)
        .post('/api/auth/register')
        .send(validUserData)
        .expect(201);

      // Try to register with same username but different email
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          ...validUserData,
          email: '<EMAIL>'
        })
        .expect(409);

      expect(response.body).toHaveProperty('error', 'User already exists');
      expect(response.body.message).toContain('username');
    });
  });

  describe('POST /api/auth/login', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'Test123!@#',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User'
    };

    beforeEach(async () => {
      // Register user before each login test
      await request(app)
        .post('/api/auth/register')
        .send(userData);

      // Verify email to allow login
      await db('users')
        .where({ email: userData.email })
        .update({ email_verified: true });
    });

    it('should login successfully with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password
        })
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Login successful');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user).toHaveProperty('email', userData.email);
      expect(response.body.data.user).not.toHaveProperty('password_hash');
    });

    it('should return 401 for invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: userData.password
        })
        .expect(401);

      expect(response.body).toHaveProperty('error', 'Authentication failed');
      expect(response.body).toHaveProperty('message', 'Invalid credentials');
    });

    it('should return 401 for invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body).toHaveProperty('error', 'Authentication failed');
      expect(response.body).toHaveProperty('message', 'Invalid credentials');
    });

    it('should return 403 for unverified email', async () => {
      // Set email as unverified
      await db('users')
        .where({ email: userData.email })
        .update({ email_verified: false });

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password
        })
        .expect(200); // Login succeeds but access is restricted

      // Try to access protected route
      const protectedResponse = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${response.body.data.token}`)
        .expect(403);

      expect(protectedResponse.body).toHaveProperty('error', 'Email not verified');
    });

    it('should lock account after 5 failed attempts', async () => {
      // Make 5 failed login attempts
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({
            email: userData.email,
            password: 'wrongpassword'
          })
          .expect(401);
      }

      // 6th attempt should return account locked message
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.message).toContain('locked');
    });
  });

  describe('POST /api/auth/logout', () => {
    let authToken;

    beforeEach(async () => {
      // Register and login user
      const userData = {
        email: '<EMAIL>',
        password: 'Test123!@#',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData);

      await db('users')
        .where({ email: userData.email })
        .update({ email_verified: true });

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password
        });

      authToken = loginResponse.body.data.token;
    });

    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Logout successful');

      // Verify token is blacklisted
      const blacklistedTokens = await db('blacklisted_tokens').select('*');
      expect(blacklistedTokens.length).toBeGreaterThan(0);
    });

    it('should work without token', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Logout successful');
    });
  });

  describe('POST /api/auth/verify-email', () => {
    let verificationToken;

    beforeEach(async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Test123!@#',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData);

      // Get verification token from database
      const user = await db('users').where({ email: userData.email }).first();
      verificationToken = user.email_verification_token;
    });

    it('should verify email successfully', async () => {
      const response = await request(app)
        .post('/api/auth/verify-email')
        .send({ token: verificationToken })
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Email verified successfully');

      // Verify user is marked as verified
      const user = await db('users').where({ email_verification_token: verificationToken }).first();
      expect(user.email_verified).toBe(true);
      expect(user.email_verification_token).toBeNull();
    });

    it('should return 400 for invalid token', async () => {
      const response = await request(app)
        .post('/api/auth/verify-email')
        .send({ token: 'invalid-token' })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Invalid token');
    });

    it('should return 400 for missing token', async () => {
      const response = await request(app)
        .post('/api/auth/verify-email')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Token required');
    });
  });

  describe('POST /api/auth/forgot-password', () => {
    beforeEach(async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Test123!@#',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData);
    });

    it('should send password reset email for existing user', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Password reset email sent');

      // Verify reset token was set
      const user = await db('users').where({ email: '<EMAIL>' }).first();
      expect(user.password_reset_token).toBeTruthy();
      expect(user.password_reset_expires).toBeTruthy();
    });

    it('should return success even for non-existent email', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({ email: '<EMAIL>' })
        .expect(200);

      expect(response.body).toHaveProperty('message');
    });

    it('should return 400 for missing email', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Email required');
    });
  });
});
