const jwt = require('jsonwebtoken');
const logger = require('../config/logger');
const redis = require('../config/redis');
const PriceService = require('./price');

class WebSocketService {
  constructor(io) {
    this.io = io;
    this.priceService = new PriceService();
    this.connectedUsers = new Map(); // userId -> socket mapping
    this.priceSubscriptions = new Map(); // symbol -> Set of socketIds
    this.portfolioSubscriptions = new Map(); // portfolioId -> Set of socketIds
    this.userRooms = new Map(); // userId -> Set of room names
    
    // Price update intervals
    this.priceUpdateInterval = null;
    this.priceUpdateFrequency = 30000; // 30 seconds
  }

  initialize() {
    this.io.on('connection', (socket) => {
      logger.info(`WebSocket connection established: ${socket.id}`);
      
      // Handle authentication
      socket.on('authenticate', async (data) => {
        await this.handleAuthentication(socket, data);
      });

      // Handle price subscriptions
      socket.on('subscribe_prices', (data) => {
        this.handlePriceSubscription(socket, data);
      });

      socket.on('unsubscribe_prices', (data) => {
        this.handlePriceUnsubscription(socket, data);
      });

      // Handle portfolio subscriptions
      socket.on('subscribe_portfolio', (data) => {
        this.handlePortfolioSubscription(socket, data);
      });

      socket.on('unsubscribe_portfolio', (data) => {
        this.handlePortfolioUnsubscription(socket, data);
      });

      // Handle social features
      socket.on('join_social_feed', () => {
        this.handleSocialFeedJoin(socket);
      });

      socket.on('leave_social_feed', () => {
        this.handleSocialFeedLeave(socket);
      });

      // Handle notifications
      socket.on('mark_notification_read', (data) => {
        this.handleNotificationRead(socket, data);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnection(socket);
      });

      // Handle errors
      socket.on('error', (error) => {
        logger.errorWithContext(error, { socketId: socket.id });
      });
    });

    // Start price update service
    this.startPriceUpdates();
    
    logger.info('WebSocket service initialized');
  }

  async handleAuthentication(socket, data) {
    try {
      const { token } = data;
      
      if (!token) {
        socket.emit('auth_error', { message: 'Token required' });
        return;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
      
      // Check if token is blacklisted
      const blacklistedToken = await redis.get(`blacklist:${decoded.jti}`);
      if (blacklistedToken) {
        socket.emit('auth_error', { message: 'Token is blacklisted' });
        return;
      }

      // Store user info in socket
      socket.userId = decoded.id;
      socket.userEmail = decoded.email;
      socket.userRole = decoded.role;
      socket.authenticated = true;

      // Add to connected users
      this.connectedUsers.set(decoded.id, socket);

      // Join user-specific room
      socket.join(`user:${decoded.id}`);
      
      // Initialize user rooms tracking
      if (!this.userRooms.has(decoded.id)) {
        this.userRooms.set(decoded.id, new Set());
      }
      this.userRooms.get(decoded.id).add(`user:${decoded.id}`);

      socket.emit('authenticated', { 
        userId: decoded.id,
        message: 'Successfully authenticated' 
      });

      logger.info(`User authenticated via WebSocket: ${decoded.email} (${socket.id})`);
    } catch (error) {
      logger.errorWithContext(error, { socketId: socket.id });
      socket.emit('auth_error', { message: 'Invalid token' });
    }
  }

  handlePriceSubscription(socket, data) {
    if (!socket.authenticated) {
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    const { symbols } = data;
    
    if (!Array.isArray(symbols)) {
      socket.emit('error', { message: 'Symbols must be an array' });
      return;
    }

    symbols.forEach(symbol => {
      const normalizedSymbol = symbol.toUpperCase();
      
      if (!this.priceSubscriptions.has(normalizedSymbol)) {
        this.priceSubscriptions.set(normalizedSymbol, new Set());
      }
      
      this.priceSubscriptions.get(normalizedSymbol).add(socket.id);
      socket.join(`price:${normalizedSymbol}`);
    });

    socket.emit('price_subscription_confirmed', { symbols });
    logger.info(`Price subscription added for ${symbols.join(', ')} (${socket.id})`);
  }

  handlePriceUnsubscription(socket, data) {
    const { symbols } = data;
    
    if (!Array.isArray(symbols)) {
      return;
    }

    symbols.forEach(symbol => {
      const normalizedSymbol = symbol.toUpperCase();
      
      if (this.priceSubscriptions.has(normalizedSymbol)) {
        this.priceSubscriptions.get(normalizedSymbol).delete(socket.id);
        
        // Clean up empty subscriptions
        if (this.priceSubscriptions.get(normalizedSymbol).size === 0) {
          this.priceSubscriptions.delete(normalizedSymbol);
        }
      }
      
      socket.leave(`price:${normalizedSymbol}`);
    });

    socket.emit('price_unsubscription_confirmed', { symbols });
  }

  handlePortfolioSubscription(socket, data) {
    if (!socket.authenticated) {
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    const { portfolioId } = data;
    
    if (!portfolioId) {
      socket.emit('error', { message: 'Portfolio ID required' });
      return;
    }

    // TODO: Verify user owns this portfolio
    
    if (!this.portfolioSubscriptions.has(portfolioId)) {
      this.portfolioSubscriptions.set(portfolioId, new Set());
    }
    
    this.portfolioSubscriptions.get(portfolioId).add(socket.id);
    socket.join(`portfolio:${portfolioId}`);
    
    // Add to user rooms tracking
    if (this.userRooms.has(socket.userId)) {
      this.userRooms.get(socket.userId).add(`portfolio:${portfolioId}`);
    }

    socket.emit('portfolio_subscription_confirmed', { portfolioId });
    logger.info(`Portfolio subscription added for ${portfolioId} (${socket.id})`);
  }

  handlePortfolioUnsubscription(socket, data) {
    const { portfolioId } = data;
    
    if (this.portfolioSubscriptions.has(portfolioId)) {
      this.portfolioSubscriptions.get(portfolioId).delete(socket.id);
      
      if (this.portfolioSubscriptions.get(portfolioId).size === 0) {
        this.portfolioSubscriptions.delete(portfolioId);
      }
    }
    
    socket.leave(`portfolio:${portfolioId}`);
    
    // Remove from user rooms tracking
    if (this.userRooms.has(socket.userId)) {
      this.userRooms.get(socket.userId).delete(`portfolio:${portfolioId}`);
    }

    socket.emit('portfolio_unsubscription_confirmed', { portfolioId });
  }

  handleSocialFeedJoin(socket) {
    if (!socket.authenticated) {
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    socket.join('social_feed');
    
    if (this.userRooms.has(socket.userId)) {
      this.userRooms.get(socket.userId).add('social_feed');
    }

    socket.emit('social_feed_joined');
    logger.info(`User joined social feed: ${socket.userEmail} (${socket.id})`);
  }

  handleSocialFeedLeave(socket) {
    socket.leave('social_feed');
    
    if (this.userRooms.has(socket.userId)) {
      this.userRooms.get(socket.userId).delete('social_feed');
    }

    socket.emit('social_feed_left');
  }

  handleNotificationRead(socket, data) {
    if (!socket.authenticated) {
      return;
    }

    const { notificationId } = data;
    
    // Broadcast to all user's connected devices
    this.io.to(`user:${socket.userId}`).emit('notification_read', { notificationId });
  }

  handleDisconnection(socket) {
    logger.info(`WebSocket disconnection: ${socket.id}`);
    
    if (socket.userId) {
      // Remove from connected users
      this.connectedUsers.delete(socket.userId);
      
      // Clean up subscriptions
      this.cleanupSocketSubscriptions(socket);
      
      // Clean up user rooms
      this.userRooms.delete(socket.userId);
    }
  }

  cleanupSocketSubscriptions(socket) {
    // Clean up price subscriptions
    for (const [symbol, sockets] of this.priceSubscriptions.entries()) {
      sockets.delete(socket.id);
      if (sockets.size === 0) {
        this.priceSubscriptions.delete(symbol);
      }
    }

    // Clean up portfolio subscriptions
    for (const [portfolioId, sockets] of this.portfolioSubscriptions.entries()) {
      sockets.delete(socket.id);
      if (sockets.size === 0) {
        this.portfolioSubscriptions.delete(portfolioId);
      }
    }
  }

  startPriceUpdates() {
    this.priceUpdateInterval = setInterval(async () => {
      await this.broadcastPriceUpdates();
    }, this.priceUpdateFrequency);
    
    logger.info('Price update service started');
  }

  async broadcastPriceUpdates() {
    try {
      const subscribedSymbols = Array.from(this.priceSubscriptions.keys());
      
      if (subscribedSymbols.length === 0) {
        return;
      }

      const prices = await this.priceService.getCurrentPrices(subscribedSymbols);
      
      for (const [symbol, price] of Object.entries(prices)) {
        if (price !== null) {
          this.io.to(`price:${symbol}`).emit('price_update', {
            symbol,
            price,
            timestamp: new Date().toISOString()
          });
        }
      }
    } catch (error) {
      logger.errorWithContext(error, { operation: 'broadcastPriceUpdates' });
    }
  }

  // Public methods for other services to use
  
  sendNotificationToUser(userId, notification) {
    this.io.to(`user:${userId}`).emit('notification', notification);
  }

  sendPortfolioUpdate(portfolioId, update) {
    this.io.to(`portfolio:${portfolioId}`).emit('portfolio_update', update);
  }

  broadcastSocialPost(post) {
    this.io.to('social_feed').emit('new_post', post);
  }

  broadcastMarketAlert(alert) {
    this.io.emit('market_alert', alert);
  }

  getConnectedUserCount() {
    return this.connectedUsers.size;
  }

  isUserConnected(userId) {
    return this.connectedUsers.has(userId);
  }

  shutdown() {
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
    }
    
    logger.info('WebSocket service shutdown');
  }
}

module.exports = WebSocketService;
