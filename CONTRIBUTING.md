# Contributing to CryptoSphere

Thank you for your interest in contributing to CryptoSphere! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Process](#contributing-process)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Documentation](#documentation)
- [Issue Reporting](#issue-reporting)
- [Pull Request Process](#pull-request-process)

## Code of Conduct

This project adheres to a code of conduct that we expect all contributors to follow. Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

## Getting Started

### Prerequisites

- Node.js 18+ and npm 8+
- PostgreSQL 14+
- Redis 7+
- Docker and Docker Compose (optional but recommended)
- Git

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/CryptoSphere.git
   cd CryptoSphere
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install Dependencies**
   ```bash
   # Backend
   cd backend && npm install
   
   # Frontend
   cd ../frontend && npm install
   ```

4. **Database Setup**
   ```bash
   cd backend
   npm run db:migrate
   npm run db:seed
   ```

5. **Start Development Servers**
   ```bash
   # Terminal 1 - Backend
   cd backend && npm run dev
   
   # Terminal 2 - Frontend
   cd frontend && npm run dev
   ```

### Using Docker (Recommended)

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## Contributing Process

1. **Check Existing Issues**: Look for existing issues or create a new one
2. **Fork the Repository**: Create your own fork
3. **Create a Branch**: Use descriptive branch names
4. **Make Changes**: Follow coding standards and write tests
5. **Test Your Changes**: Ensure all tests pass
6. **Submit Pull Request**: Follow the PR template

### Branch Naming Convention

- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `hotfix/description` - Critical fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

## Coding Standards

### Backend (Node.js)

- Use ESLint configuration provided
- Follow Airbnb JavaScript style guide
- Use async/await over Promises
- Implement proper error handling
- Add JSDoc comments for functions
- Use meaningful variable and function names

```javascript
/**
 * Get user portfolio by ID
 * @param {string} portfolioId - Portfolio UUID
 * @param {string} userId - User UUID
 * @returns {Promise<Object|null>} Portfolio object or null
 */
async function getPortfolioById(portfolioId, userId) {
  try {
    // Implementation
  } catch (error) {
    logger.errorWithContext(error, { portfolioId, userId });
    throw error;
  }
}
```

### Frontend (React/TypeScript)

- Use TypeScript for type safety
- Follow React best practices and hooks
- Use functional components over class components
- Implement proper prop types
- Use meaningful component and variable names
- Follow the established folder structure

```typescript
interface PortfolioCardProps {
  portfolio: Portfolio;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

const PortfolioCard: React.FC<PortfolioCardProps> = ({
  portfolio,
  onEdit,
  onDelete
}) => {
  // Implementation
};
```

### Database

- Use descriptive table and column names
- Include proper indexes
- Add foreign key constraints
- Use UUIDs for primary keys
- Include created_at and updated_at timestamps
- Implement soft deletes where appropriate

## Testing Guidelines

### Backend Testing

- Write unit tests for all services and utilities
- Write integration tests for API endpoints
- Use Jest as the testing framework
- Aim for >80% code coverage
- Mock external dependencies

```javascript
describe('PortfolioService', () => {
  describe('createPortfolio', () => {
    it('should create portfolio successfully', async () => {
      // Test implementation
    });
    
    it('should throw error for invalid data', async () => {
      // Test implementation
    });
  });
});
```

### Frontend Testing

- Write unit tests for components and hooks
- Write integration tests for user flows
- Use React Testing Library
- Test user interactions and edge cases
- Mock API calls

```typescript
describe('PortfolioCard', () => {
  it('should render portfolio information', () => {
    render(<PortfolioCard portfolio={mockPortfolio} />);
    expect(screen.getByText(mockPortfolio.name)).toBeInTheDocument();
  });
});
```

### Running Tests

```bash
# Backend tests
cd backend
npm test
npm run test:coverage

# Frontend tests
cd frontend
npm test
npm run test:coverage

# E2E tests
npm run test:e2e
```

## Documentation

- Update README.md for significant changes
- Add JSDoc comments for functions
- Update API documentation
- Include code examples
- Document configuration options

## Issue Reporting

When reporting issues, please include:

- Clear description of the problem
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, Node version, etc.)
- Screenshots or error logs if applicable

### Issue Labels

- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Improvements or additions to documentation
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention is needed
- `priority:high` - High priority issue
- `priority:low` - Low priority issue

## Pull Request Process

1. **Update Documentation**: Update relevant documentation
2. **Add Tests**: Include tests for new functionality
3. **Update Changelog**: Add entry to CHANGELOG.md
4. **Check CI**: Ensure all CI checks pass
5. **Request Review**: Request review from maintainers

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
```

## Development Workflow

### Git Workflow

1. Create feature branch from `develop`
2. Make changes and commit with descriptive messages
3. Push branch and create pull request
4. Address review feedback
5. Merge to `develop` after approval
6. Deploy to staging for testing
7. Merge to `main` for production release

### Commit Messages

Use conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Formatting changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

Examples:
```
feat(portfolio): add portfolio sharing functionality
fix(auth): resolve token expiration issue
docs(api): update authentication endpoints
```

## Release Process

1. Create release branch from `develop`
2. Update version numbers
3. Update CHANGELOG.md
4. Create pull request to `main`
5. Tag release after merge
6. Deploy to production
7. Merge back to `develop`

## Getting Help

- Join our [Discord community](https://discord.gg/cryptosphere)
- Check existing [GitHub Issues](https://github.com/HectorTa1989/CryptoSphere/issues)
- Read the [documentation](https://docs.cryptosphere.io)
- Contact maintainers: [<EMAIL>](mailto:<EMAIL>)

## Recognition

Contributors will be recognized in:
- CONTRIBUTORS.md file
- Release notes
- Project documentation
- Annual contributor highlights

Thank you for contributing to CryptoSphere! 🚀
