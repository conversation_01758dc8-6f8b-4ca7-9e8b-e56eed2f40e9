name: Release

on:
  push:
    tags:
      - 'v*'

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Create GitHub Release
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      release_id: ${{ steps.create_release.outputs.id }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog from git commits
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD~1 2>/dev/null || echo "")
          if [ -z "$PREVIOUS_TAG" ]; then
            CHANGELOG=$(git log --pretty=format:"- %s" --no-merges)
          else
            CHANGELOG=$(git log ${PREVIOUS_TAG}..HEAD --pretty=format:"- %s" --no-merges)
          fi
          echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          body: |
            ## Changes in this Release
            ${{ steps.changelog.outputs.CHANGELOG }}
            
            ## Docker Images
            - Backend: `${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ github.ref_name }}`
            - Frontend: `${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ github.ref_name }}`
            
            ## Installation
            ```bash
            git clone https://github.com/${{ github.repository }}.git
            cd CryptoSphere
            git checkout ${{ github.ref_name }}
            docker-compose up -d
            ```
          draft: false
          prerelease: ${{ contains(github.ref, 'alpha') || contains(github.ref, 'beta') || contains(github.ref, 'rc') }}

  # Build and Push Release Images
  build-release:
    name: Build Release Images
    runs-on: ubuntu-latest
    needs: [create-release]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract version from tag
        id: version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Build and push backend release image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ steps.version.outputs.VERSION }}
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: production

      - name: Build and push frontend release image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ steps.version.outputs.VERSION }}
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: production

  # Create Distribution Package
  create-package:
    name: Create Distribution Package
    runs-on: ubuntu-latest
    needs: [create-release]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install backend dependencies
        working-directory: ./backend
        run: npm ci --only=production

      - name: Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Build frontend
        working-directory: ./frontend
        run: npm run build

      - name: Create distribution package
        run: |
          mkdir -p dist
          
          # Copy backend files
          cp -r backend dist/
          rm -rf dist/backend/node_modules
          rm -rf dist/backend/tests
          rm -rf dist/backend/coverage
          
          # Copy frontend build
          cp -r frontend/.next dist/frontend-build
          cp -r frontend/public dist/frontend-public
          cp frontend/package.json dist/frontend-package.json
          
          # Copy configuration files
          cp docker-compose.yml dist/
          cp .env.example dist/
          cp README.md dist/
          
          # Create archive
          tar -czf cryptosphere-${{ github.ref_name }}.tar.gz -C dist .

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ./cryptosphere-${{ github.ref_name }}.tar.gz
          asset_name: cryptosphere-${{ github.ref_name }}.tar.gz
          asset_content_type: application/gzip

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-release]
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to production
        run: |
          echo "Deploying release ${{ github.ref_name }} to production..."
          # Add your production deployment commands here

  # Update Documentation
  update-docs:
    name: Update Documentation
    runs-on: ubuntu-latest
    needs: [create-release]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Generate API documentation
        working-directory: ./backend
        run: |
          npm ci
          npm run docs:generate

      - name: Deploy documentation
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/build
          destination_dir: ${{ github.ref_name }}

  # Notify Release
  notify-release:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [create-release, build-release, deploy-production]
    if: always()

    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#releases'
          text: |
            🚀 New release published: ${{ github.ref_name }}
            📦 Release URL: ${{ needs.create-release.outputs.html_url }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Discord
        uses: Ilshidur/action-discord@master
        with:
          args: |
            🎉 **CryptoSphere Release ${{ github.ref_name }}**
            
            A new version has been released and deployed to production!
            
            🔗 [View Release](https://github.com/${{ github.repository }}/releases/tag/${{ github.ref_name }})
            📚 [Documentation](https://${{ github.repository_owner }}.github.io/CryptoSphere/${{ github.ref_name }})
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
