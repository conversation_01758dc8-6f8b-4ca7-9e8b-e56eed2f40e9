/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('portfolios', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to users
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Portfolio information
    table.string('name', 100).notNullable();
    table.text('description').nullable();
    table.string('base_currency', 10).defaultTo('USD');
    table.boolean('is_public').defaultTo(false);
    table.boolean('is_default').defaultTo(false);
    
    // Portfolio settings
    table.decimal('total_invested', 20, 8).defaultTo(0);
    table.decimal('current_value', 20, 8).defaultTo(0);
    table.decimal('total_pnl', 20, 8).defaultTo(0);
    table.decimal('total_pnl_percentage', 10, 4).defaultTo(0);
    
    // Performance tracking
    table.decimal('daily_change', 20, 8).defaultTo(0);
    table.decimal('daily_change_percentage', 10, 4).defaultTo(0);
    table.decimal('weekly_change', 20, 8).defaultTo(0);
    table.decimal('weekly_change_percentage', 10, 4).defaultTo(0);
    table.decimal('monthly_change', 20, 8).defaultTo(0);
    table.decimal('monthly_change_percentage', 10, 4).defaultTo(0);
    table.decimal('yearly_change', 20, 8).defaultTo(0);
    table.decimal('yearly_change_percentage', 10, 4).defaultTo(0);
    
    // Risk metrics
    table.decimal('volatility', 10, 4).defaultTo(0);
    table.decimal('sharpe_ratio', 10, 4).defaultTo(0);
    table.decimal('max_drawdown', 10, 4).defaultTo(0);
    table.decimal('beta', 10, 4).defaultTo(0);
    
    // Metadata
    table.json('tags').nullable();
    table.json('metadata').nullable();
    table.timestamp('last_rebalanced').nullable();
    table.timestamp('last_updated').nullable();
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['is_public']);
    table.index(['is_default']);
    table.index(['base_currency']);
    table.index(['created_at']);
    table.index(['is_deleted']);
    table.index(['user_id', 'is_deleted']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('portfolios');
};
