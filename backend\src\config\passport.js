const JwtStrategy = require('passport-jwt').Strategy;
const ExtractJwt = require('passport-jwt').ExtractJwt;
const LocalStrategy = require('passport-local').Strategy;
const bcrypt = require('bcryptjs');
const db = require('./database');
const logger = require('./logger');

module.exports = (passport) => {
  // JWT Strategy
  const jwtOptions = {
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: process.env.JWT_SECRET || 'your-secret-key',
    algorithms: ['HS256'],
  };

  passport.use(
    new JwtStrategy(jwtOptions, async (payload, done) => {
      try {
        // Find user by ID from JWT payload
        const user = await db('users')
          .select('id', 'email', 'username', 'first_name', 'last_name', 'is_active', 'role', 'email_verified')
          .where({ id: payload.id, is_active: true })
          .first();

        if (user) {
          // Check if token is not blacklisted
          const blacklistedToken = await db('blacklisted_tokens')
            .where({ token: payload.jti })
            .first();

          if (blacklistedToken) {
            logger.security('Blacklisted token used', { userId: user.id, email: user.email });
            return done(null, false);
          }

          // Update last activity
          await db('users')
            .where({ id: user.id })
            .update({ last_activity: new Date() });

          return done(null, user);
        }

        logger.security('JWT authentication failed - user not found', { userId: payload.id });
        return done(null, false);
      } catch (error) {
        logger.errorWithContext(error, { strategy: 'JWT', userId: payload.id });
        return done(error, false);
      }
    })
  );

  // Local Strategy for login
  passport.use(
    new LocalStrategy(
      {
        usernameField: 'email',
        passwordField: 'password',
      },
      async (email, password, done) => {
        try {
          // Find user by email
          const user = await db('users')
            .select('id', 'email', 'username', 'password_hash', 'first_name', 'last_name', 
                   'is_active', 'role', 'email_verified', 'failed_login_attempts', 
                   'locked_until', 'two_factor_enabled', 'two_factor_secret')
            .where({ email: email.toLowerCase() })
            .first();

          if (!user) {
            logger.security('Login attempt with non-existent email', { email });
            return done(null, false, { message: 'Invalid credentials' });
          }

          // Check if account is locked
          if (user.locked_until && new Date() < user.locked_until) {
            logger.security('Login attempt on locked account', { userId: user.id, email });
            return done(null, false, { message: 'Account is temporarily locked' });
          }

          // Check if account is active
          if (!user.is_active) {
            logger.security('Login attempt on inactive account', { userId: user.id, email });
            return done(null, false, { message: 'Account is deactivated' });
          }

          // Verify password
          const isValidPassword = await bcrypt.compare(password, user.password_hash);
          
          if (!isValidPassword) {
            // Increment failed login attempts
            const failedAttempts = (user.failed_login_attempts || 0) + 1;
            const updateData = { failed_login_attempts: failedAttempts };

            // Lock account after 5 failed attempts
            if (failedAttempts >= 5) {
              updateData.locked_until = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
              logger.security('Account locked due to failed login attempts', { userId: user.id, email });
            }

            await db('users').where({ id: user.id }).update(updateData);
            
            logger.security('Failed login attempt', { userId: user.id, email, failedAttempts });
            return done(null, false, { message: 'Invalid credentials' });
          }

          // Reset failed login attempts on successful login
          if (user.failed_login_attempts > 0) {
            await db('users')
              .where({ id: user.id })
              .update({ 
                failed_login_attempts: 0, 
                locked_until: null,
                last_login: new Date()
              });
          }

          // Remove password hash from user object
          delete user.password_hash;

          logger.business('User login successful', { userId: user.id, email: user.email });
          return done(null, user);

        } catch (error) {
          logger.errorWithContext(error, { strategy: 'Local', email });
          return done(error);
        }
      }
    )
  );

  // Serialize user for session
  passport.serializeUser((user, done) => {
    done(null, user.id);
  });

  // Deserialize user from session
  passport.deserializeUser(async (id, done) => {
    try {
      const user = await db('users')
        .select('id', 'email', 'username', 'first_name', 'last_name', 'is_active', 'role')
        .where({ id, is_active: true })
        .first();

      done(null, user);
    } catch (error) {
      logger.errorWithContext(error, { operation: 'deserializeUser', userId: id });
      done(error, null);
    }
  });
};
