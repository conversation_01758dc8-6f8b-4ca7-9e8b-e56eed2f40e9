/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('social_follows', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign keys
    table.uuid('follower_id').notNullable();
    table.foreign('follower_id').references('id').inTable('users').onDelete('CASCADE');
    
    table.uuid('following_id').notNullable();
    table.foreign('following_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Follow settings
    table.boolean('is_active').defaultTo(true);
    table.boolean('notifications_enabled').defaultTo(true);
    table.enum('notification_type', ['all', 'posts_only', 'trades_only', 'none']).defaultTo('all');
    
    // Metadata
    table.json('metadata').nullable();
    table.timestamp('unfollowed_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['follower_id']);
    table.index(['following_id']);
    table.index(['is_active']);
    table.index(['created_at']);
    table.index(['follower_id', 'is_active']);
    table.index(['following_id', 'is_active']);
    
    // Unique constraint to prevent duplicate follows
    table.unique(['follower_id', 'following_id'], 'unique_follow_relationship');
    
    // Prevent self-following
    table.check('follower_id != following_id', 'no_self_follow');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('social_follows');
};
