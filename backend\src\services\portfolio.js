const { v4: uuidv4 } = require('uuid');
const db = require('../config/database');
const redis = require('../config/redis');
const logger = require('../config/logger');
const { AppError } = require('../middleware/errorHandler');
const PriceService = require('./price');

class PortfolioService {
  constructor() {
    this.priceService = new PriceService();
  }

  // Get all portfolios for a user
  async getUserPortfolios(userId) {
    try {
      const portfolios = await db('portfolios')
        .select('*')
        .where({ user_id: userId, is_deleted: false })
        .orderBy('created_at', 'desc');

      // Get portfolio summaries
      const portfoliosWithSummary = await Promise.all(
        portfolios.map(async (portfolio) => {
          const summary = await this.getPortfolioSummary(portfolio.id);
          return {
            ...portfolio,
            summary
          };
        })
      );

      return portfoliosWithSummary;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getUserPortfolios', userId });
      throw error;
    }
  }

  // Create new portfolio
  async createPortfolio(portfolioData) {
    try {
      const portfolioId = uuidv4();
      
      const portfolio = {
        id: portfolioId,
        user_id: portfolioData.userId,
        name: portfolioData.name,
        description: portfolioData.description || null,
        base_currency: portfolioData.currency || 'USD',
        is_public: portfolioData.isPublic || false,
        created_at: new Date(),
        updated_at: new Date()
      };

      await db('portfolios').insert(portfolio);

      // Create default allocation if needed
      await this.initializePortfolioSettings(portfolioId);

      return await this.getPortfolioById(portfolioId, portfolioData.userId);
    } catch (error) {
      logger.errorWithContext(error, { operation: 'createPortfolio', userId: portfolioData.userId });
      throw error;
    }
  }

  // Get portfolio by ID
  async getPortfolioById(portfolioId, userId) {
    try {
      const portfolio = await db('portfolios')
        .select('*')
        .where({ 
          id: portfolioId, 
          user_id: userId, 
          is_deleted: false 
        })
        .first();

      if (!portfolio) {
        return null;
      }

      // Get portfolio summary and holdings
      const [summary, holdings, performance] = await Promise.all([
        this.getPortfolioSummary(portfolioId),
        this.getPortfolioHoldings(portfolioId, userId),
        this.getPortfolioPerformance(portfolioId)
      ]);

      return {
        ...portfolio,
        summary,
        holdings,
        performance
      };
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getPortfolioById', portfolioId, userId });
      throw error;
    }
  }

  // Update portfolio
  async updatePortfolio(portfolioId, updateData, userId) {
    try {
      const existingPortfolio = await db('portfolios')
        .where({ id: portfolioId, user_id: userId, is_deleted: false })
        .first();

      if (!existingPortfolio) {
        return null;
      }

      const updatedData = {
        name: updateData.name,
        description: updateData.description,
        base_currency: updateData.currency || existingPortfolio.base_currency,
        is_public: updateData.isPublic !== undefined ? updateData.isPublic : existingPortfolio.is_public,
        updated_at: new Date()
      };

      await db('portfolios')
        .where({ id: portfolioId })
        .update(updatedData);

      return await this.getPortfolioById(portfolioId, userId);
    } catch (error) {
      logger.errorWithContext(error, { operation: 'updatePortfolio', portfolioId, userId });
      throw error;
    }
  }

  // Delete portfolio (soft delete)
  async deletePortfolio(portfolioId, userId) {
    try {
      const result = await db('portfolios')
        .where({ id: portfolioId, user_id: userId, is_deleted: false })
        .update({ 
          is_deleted: true, 
          deleted_at: new Date(),
          updated_at: new Date()
        });

      return result > 0;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'deletePortfolio', portfolioId, userId });
      throw error;
    }
  }

  // Get portfolio holdings
  async getPortfolioHoldings(portfolioId, userId) {
    try {
      // Verify portfolio ownership
      const portfolio = await db('portfolios')
        .where({ id: portfolioId, user_id: userId, is_deleted: false })
        .first();

      if (!portfolio) {
        throw new AppError('Portfolio not found', 404);
      }

      const holdings = await db('portfolio_holdings')
        .select('*')
        .where({ portfolio_id: portfolioId, is_deleted: false })
        .orderBy('created_at', 'desc');

      // Get current prices and calculate values
      const holdingsWithValues = await Promise.all(
        holdings.map(async (holding) => {
          const currentPrice = await this.priceService.getCurrentPrice(holding.symbol);
          const currentValue = holding.amount * (currentPrice || 0);
          const totalCost = holding.amount * (holding.average_price || 0);
          const pnl = currentValue - totalCost;
          const pnlPercentage = totalCost > 0 ? (pnl / totalCost) * 100 : 0;

          return {
            ...holding,
            current_price: currentPrice,
            current_value: currentValue,
            total_cost: totalCost,
            pnl,
            pnl_percentage: pnlPercentage
          };
        })
      );

      return holdingsWithValues;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getPortfolioHoldings', portfolioId, userId });
      throw error;
    }
  }

  // Add holding to portfolio
  async addHolding(holdingData, userId) {
    try {
      // Verify portfolio ownership
      const portfolio = await db('portfolios')
        .where({ id: holdingData.portfolioId, user_id: userId, is_deleted: false })
        .first();

      if (!portfolio) {
        throw new AppError('Portfolio not found', 404);
      }

      // Check if holding already exists
      const existingHolding = await db('portfolio_holdings')
        .where({ 
          portfolio_id: holdingData.portfolioId, 
          symbol: holdingData.symbol.toUpperCase(),
          exchange: holdingData.exchange || null,
          is_deleted: false 
        })
        .first();

      if (existingHolding) {
        // Update existing holding
        const newAmount = existingHolding.amount + holdingData.amount;
        const newAveragePrice = holdingData.averagePrice || existingHolding.average_price;
        
        // Calculate weighted average price if both have prices
        if (existingHolding.average_price && holdingData.averagePrice) {
          const totalValue = (existingHolding.amount * existingHolding.average_price) + 
                           (holdingData.amount * holdingData.averagePrice);
          newAveragePrice = totalValue / newAmount;
        }

        await db('portfolio_holdings')
          .where({ id: existingHolding.id })
          .update({
            amount: newAmount,
            average_price: newAveragePrice,
            notes: holdingData.notes || existingHolding.notes,
            updated_at: new Date()
          });

        return await db('portfolio_holdings')
          .where({ id: existingHolding.id })
          .first();
      } else {
        // Create new holding
        const holdingId = uuidv4();
        const holding = {
          id: holdingId,
          portfolio_id: holdingData.portfolioId,
          symbol: holdingData.symbol.toUpperCase(),
          amount: holdingData.amount,
          average_price: holdingData.averagePrice || null,
          exchange: holdingData.exchange || null,
          notes: holdingData.notes || null,
          created_at: new Date(),
          updated_at: new Date()
        };

        await db('portfolio_holdings').insert(holding);
        return holding;
      }
    } catch (error) {
      logger.errorWithContext(error, { operation: 'addHolding', userId });
      throw error;
    }
  }

  // Update holding
  async updateHolding(holdingId, updateData, userId) {
    try {
      // Verify holding ownership through portfolio
      const holding = await db('portfolio_holdings')
        .join('portfolios', 'portfolio_holdings.portfolio_id', 'portfolios.id')
        .where({ 
          'portfolio_holdings.id': holdingId,
          'portfolios.user_id': userId,
          'portfolio_holdings.is_deleted': false,
          'portfolios.is_deleted': false
        })
        .select('portfolio_holdings.*')
        .first();

      if (!holding) {
        return null;
      }

      const updatedData = {
        symbol: updateData.symbol?.toUpperCase() || holding.symbol,
        amount: updateData.amount !== undefined ? updateData.amount : holding.amount,
        average_price: updateData.averagePrice !== undefined ? updateData.averagePrice : holding.average_price,
        exchange: updateData.exchange !== undefined ? updateData.exchange : holding.exchange,
        notes: updateData.notes !== undefined ? updateData.notes : holding.notes,
        updated_at: new Date()
      };

      await db('portfolio_holdings')
        .where({ id: holdingId })
        .update(updatedData);

      return await db('portfolio_holdings')
        .where({ id: holdingId })
        .first();
    } catch (error) {
      logger.errorWithContext(error, { operation: 'updateHolding', holdingId, userId });
      throw error;
    }
  }

  // Delete holding (soft delete)
  async deleteHolding(holdingId, userId) {
    try {
      const result = await db('portfolio_holdings')
        .join('portfolios', 'portfolio_holdings.portfolio_id', 'portfolios.id')
        .where({ 
          'portfolio_holdings.id': holdingId,
          'portfolios.user_id': userId,
          'portfolio_holdings.is_deleted': false,
          'portfolios.is_deleted': false
        })
        .update({ 
          'portfolio_holdings.is_deleted': true,
          'portfolio_holdings.deleted_at': new Date(),
          'portfolio_holdings.updated_at': new Date()
        });

      return result > 0;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'deleteHolding', holdingId, userId });
      throw error;
    }
  }

  // Get portfolio summary
  async getPortfolioSummary(portfolioId) {
    try {
      const cacheKey = `portfolio_summary:${portfolioId}`;
      const cached = await redis.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      const holdings = await db('portfolio_holdings')
        .where({ portfolio_id: portfolioId, is_deleted: false });

      let totalValue = 0;
      let totalCost = 0;
      let holdingCount = holdings.length;

      for (const holding of holdings) {
        const currentPrice = await this.priceService.getCurrentPrice(holding.symbol);
        const holdingValue = holding.amount * (currentPrice || 0);
        const holdingCost = holding.amount * (holding.average_price || 0);
        
        totalValue += holdingValue;
        totalCost += holdingCost;
      }

      const totalPnL = totalValue - totalCost;
      const totalPnLPercentage = totalCost > 0 ? (totalPnL / totalCost) * 100 : 0;

      const summary = {
        total_value: totalValue,
        total_cost: totalCost,
        total_pnl: totalPnL,
        total_pnl_percentage: totalPnLPercentage,
        holding_count: holdingCount,
        last_updated: new Date()
      };

      // Cache for 5 minutes
      await redis.setex(cacheKey, 300, summary);

      return summary;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getPortfolioSummary', portfolioId });
      throw error;
    }
  }

  // Initialize portfolio settings
  async initializePortfolioSettings(portfolioId) {
    try {
      const settings = {
        id: uuidv4(),
        portfolio_id: portfolioId,
        rebalance_threshold: 5.0, // 5% threshold
        auto_rebalance: false,
        risk_tolerance: 'moderate',
        created_at: new Date(),
        updated_at: new Date()
      };

      await db('portfolio_settings').insert(settings);
    } catch (error) {
      logger.errorWithContext(error, { operation: 'initializePortfolioSettings', portfolioId });
      // Don't throw error for settings initialization
    }
  }

  // Get portfolio performance
  async getPortfolioPerformance(portfolioId) {
    try {
      // This would typically involve complex calculations
      // For now, return basic performance metrics
      const performance = {
        daily_change: 0,
        weekly_change: 0,
        monthly_change: 0,
        yearly_change: 0,
        all_time_high: 0,
        all_time_low: 0,
        volatility: 0,
        sharpe_ratio: 0
      };

      return performance;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getPortfolioPerformance', portfolioId });
      return null;
    }
  }
}

module.exports = PortfolioService;
