# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=5000
FRONTEND_URL=http://localhost:3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cryptosphere_dev
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false
DATABASE_URL=postgresql://postgres:password@localhost:5432/cryptosphere_dev

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=CryptoSphere

# API Keys
COINGECKO_API_KEY=
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key
CRYPTOCOMPARE_API_KEY=
MESSARI_API_KEY=

# Exchange API Keys (Optional - for portfolio sync)
BINANCE_API_KEY=
BINANCE_SECRET_KEY=
COINBASE_API_KEY=
COINBASE_SECRET_KEY=
KRAKEN_API_KEY=
KRAKEN_SECRET_KEY=

# Blockchain RPC URLs
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your-infura-key
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-infura-key
BSC_RPC_URL=https://bsc-dataseed.binance.org/
AVALANCHE_RPC_URL=https://api.avax.network/ext/bc/C/rpc

# Web3 Configuration
INFURA_PROJECT_ID=your-infura-project-id
ALCHEMY_API_KEY=your-alchemy-api-key
MORALIS_API_KEY=your-moralis-api-key

# AWS Configuration (Optional - for file uploads)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=cryptosphere-uploads

# Stripe Configuration (Optional - for payments)
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Twitter API (Optional - for social features)
TWITTER_API_KEY=
TWITTER_API_SECRET=
TWITTER_BEARER_TOKEN=

# Telegram Bot (Optional - for notifications)
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# Discord Webhook (Optional - for notifications)
DISCORD_WEBHOOK_URL=

# Analytics
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# Monitoring
SENTRY_DSN=https://<EMAIL>/project-id
NEW_RELIC_LICENSE_KEY=your-new-relic-license-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production
CORS_ORIGIN=http://localhost:3000

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_KEYS=1000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Feature Flags
ENABLE_SOCIAL_TRADING=true
ENABLE_DEFI_INTEGRATION=true
ENABLE_AI_PREDICTIONS=true
ENABLE_TAX_REPORTING=true
ENABLE_MOBILE_NOTIFICATIONS=true

# Development Tools
ENABLE_GRAPHQL_PLAYGROUND=true
ENABLE_API_DOCS=true
ENABLE_DEBUG_MODE=false

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false

# Performance
ENABLE_COMPRESSION=true
ENABLE_CACHING=true
ENABLE_CLUSTERING=false

# Testing
TEST_DB_NAME=cryptosphere_test
TEST_REDIS_DB=1
