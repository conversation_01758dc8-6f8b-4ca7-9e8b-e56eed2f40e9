const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  // Deletes ALL existing entries
  await knex('users').del();
  
  // Hash password for admin user
  const adminPassword = await bcrypt.hash('admin123!@#', 12);
  const userPassword = await bcrypt.hash('user123!@#', 12);
  
  // Insert seed entries
  const users = [
    {
      id: uuidv4(),
      email: '<EMAIL>',
      username: 'admin',
      password_hash: adminPassword,
      first_name: 'Admin',
      last_name: 'User',
      role: 'admin',
      is_active: true,
      email_verified: true,
      email_verified_at: new Date(),
      subscription_tier: 'enterprise',
      timezone: 'UTC',
      language: 'en',
      preferences: JSON.stringify({
        theme: 'dark',
        currency: 'USD',
        notifications: {
          email: true,
          push: true,
          sms: false
        }
      }),
      notification_settings: JSON.stringify({
        price_alerts: true,
        portfolio_updates: true,
        social_interactions: true,
        system_updates: true,
        marketing: false
      }),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: uuidv4(),
      email: '<EMAIL>',
      username: 'demo_user',
      password_hash: userPassword,
      first_name: 'Demo',
      last_name: 'User',
      role: 'premium',
      is_active: true,
      email_verified: true,
      email_verified_at: new Date(),
      subscription_tier: 'premium',
      timezone: 'America/New_York',
      language: 'en',
      bio: 'Demo user for CryptoSphere platform showcasing premium features',
      preferences: JSON.stringify({
        theme: 'light',
        currency: 'USD',
        notifications: {
          email: true,
          push: true,
          sms: true
        }
      }),
      notification_settings: JSON.stringify({
        price_alerts: true,
        portfolio_updates: true,
        social_interactions: true,
        system_updates: true,
        marketing: true
      }),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: uuidv4(),
      email: '<EMAIL>',
      username: 'johndoe',
      password_hash: userPassword,
      first_name: 'John',
      last_name: 'Doe',
      role: 'user',
      is_active: true,
      email_verified: true,
      email_verified_at: new Date(),
      subscription_tier: 'free',
      timezone: 'Europe/London',
      language: 'en',
      bio: 'Crypto enthusiast and long-term investor',
      preferences: JSON.stringify({
        theme: 'dark',
        currency: 'EUR',
        notifications: {
          email: true,
          push: false,
          sms: false
        }
      }),
      notification_settings: JSON.stringify({
        price_alerts: true,
        portfolio_updates: true,
        social_interactions: false,
        system_updates: true,
        marketing: false
      }),
      created_at: new Date(),
      updated_at: new Date()
    }
  ];
  
  await knex('users').insert(users);
};
