const passport = require('passport');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const logger = require('../config/logger');
const redis = require('../config/redis');

// JWT Authentication middleware
const authenticateJWT = (req, res, next) => {
  passport.authenticate('jwt', { session: false }, (err, user, info) => {
    if (err) {
      logger.errorWithContext(err, { middleware: 'authenticateJWT', path: req.path });
      return res.status(500).json({
        error: 'Authentication error',
        message: 'Internal server error during authentication'
      });
    }

    if (!user) {
      logger.security('JWT authentication failed', { 
        path: req.path, 
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        info: info?.message 
      });
      
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid or expired token'
      });
    }

    // Check if email is verified for protected routes
    if (!user.email_verified && req.path !== '/api/auth/verify-email') {
      return res.status(403).json({
        error: 'Email not verified',
        message: 'Please verify your email address to access this resource'
      });
    }

    req.user = user;
    next();
  })(req, res, next);
};

// Role-based authorization middleware
const authorize = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    // Convert single role to array
    if (typeof roles === 'string') {
      roles = [roles];
    }

    // Check if user has required role
    if (roles.length > 0 && !roles.includes(req.user.role)) {
      logger.security('Authorization failed - insufficient permissions', {
        userId: req.user.id,
        userRole: req.user.role,
        requiredRoles: roles,
        path: req.path
      });

      return res.status(403).json({
        error: 'Forbidden',
        message: 'Insufficient permissions to access this resource'
      });
    }

    next();
  };
};

// Two-factor authentication middleware
const requireTwoFactor = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    // Check if 2FA is enabled for user
    const user = await db('users')
      .select('two_factor_enabled', 'two_factor_verified')
      .where({ id: req.user.id })
      .first();

    if (user.two_factor_enabled && !user.two_factor_verified) {
      return res.status(403).json({
        error: 'Two-factor authentication required',
        message: 'Please complete two-factor authentication'
      });
    }

    next();
  } catch (error) {
    logger.errorWithContext(error, { middleware: 'requireTwoFactor', userId: req.user.id });
    res.status(500).json({
      error: 'Internal server error',
      message: 'Error verifying two-factor authentication'
    });
  }
};

// Rate limiting for authentication endpoints
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts',
    message: 'Please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use IP + email for more granular rate limiting
    const email = req.body.email || req.body.username || 'unknown';
    return `${req.ip}:${email}`;
  },
  handler: (req, res) => {
    logger.security('Rate limit exceeded for authentication', {
      ip: req.ip,
      email: req.body.email,
      path: req.path
    });
    
    res.status(429).json({
      error: 'Too many authentication attempts',
      message: 'Please try again later',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  }
});

// API rate limiting for authenticated users
const apiRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // limit each user to 100 requests per minute
  message: {
    error: 'Rate limit exceeded',
    message: 'Too many requests, please slow down'
  },
  keyGenerator: (req) => {
    return req.user ? `user:${req.user.id}` : req.ip;
  },
  handler: (req, res) => {
    logger.security('API rate limit exceeded', {
      userId: req.user?.id,
      ip: req.ip,
      path: req.path
    });
    
    res.status(429).json({
      error: 'Rate limit exceeded',
      message: 'Too many requests, please slow down',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  }
});

// Session validation middleware
const validateSession = async (req, res, next) => {
  try {
    if (!req.user) {
      return next();
    }

    // Check if session is still valid in Redis
    const sessionKey = `session:${req.user.id}`;
    const sessionData = await redis.get(sessionKey);

    if (!sessionData) {
      logger.security('Invalid session detected', { userId: req.user.id });
      return res.status(401).json({
        error: 'Session expired',
        message: 'Please log in again'
      });
    }

    // Update session expiry
    await redis.setex(sessionKey, 24 * 60 * 60, sessionData); // 24 hours

    next();
  } catch (error) {
    logger.errorWithContext(error, { middleware: 'validateSession', userId: req.user?.id });
    next(); // Continue even if session validation fails
  }
};

// IP whitelist middleware (for admin endpoints)
const ipWhitelist = (allowedIPs = []) => {
  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (allowedIPs.length > 0 && !allowedIPs.includes(clientIP)) {
      logger.security('IP not whitelisted', { ip: clientIP, path: req.path });
      return res.status(403).json({
        error: 'Access denied',
        message: 'Your IP address is not authorized to access this resource'
      });
    }

    next();
  };
};

// Request logging middleware
const logRequest = (req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logData = {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      userId: req.user?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    };

    if (res.statusCode >= 400) {
      logger.error('Request failed', logData);
    } else {
      logger.http('Request completed', logData);
    }
  });

  next();
};

module.exports = {
  authenticateJWT,
  authorize,
  requireTwoFactor,
  authRateLimit,
  apiRateLimit,
  validateSession,
  ipWhitelist,
  logRequest
};
