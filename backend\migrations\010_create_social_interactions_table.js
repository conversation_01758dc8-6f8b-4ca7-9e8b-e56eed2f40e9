/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('social_interactions', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign keys
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    table.uuid('post_id').notNullable();
    table.foreign('post_id').references('id').inTable('social_posts').onDelete('CASCADE');
    
    // Interaction details
    table.enum('type', ['like', 'comment', 'share', 'view', 'bookmark', 'report']).notNullable();
    table.text('content').nullable(); // For comments
    table.uuid('parent_comment_id').nullable(); // For nested comments
    table.foreign('parent_comment_id').references('id').inTable('social_interactions');
    
    // Interaction metadata
    table.json('metadata').nullable();
    table.boolean('is_active').defaultTo(true); // For soft delete of comments/likes
    
    // Moderation
    table.boolean('is_flagged').defaultTo(false);
    table.text('flag_reason').nullable();
    table.uuid('flagged_by').nullable();
    table.foreign('flagged_by').references('id').inTable('users');
    table.timestamp('flagged_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['post_id']);
    table.index(['type']);
    table.index(['parent_comment_id']);
    table.index(['is_active']);
    table.index(['is_flagged']);
    table.index(['created_at']);
    table.index(['post_id', 'type']);
    table.index(['user_id', 'type']);
    table.index(['post_id', 'type', 'is_active']);
    
    // Unique constraint for likes (one like per user per post)
    table.unique(['user_id', 'post_id', 'type'], 'unique_user_post_interaction');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('social_interactions');
};
