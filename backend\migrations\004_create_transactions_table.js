/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('transactions', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign keys
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    table.uuid('portfolio_id').notNullable();
    table.foreign('portfolio_id').references('id').inTable('portfolios').onDelete('CASCADE');
    
    // Transaction details
    table.enum('type', ['buy', 'sell', 'transfer_in', 'transfer_out', 'stake', 'unstake', 'reward', 'fee', 'swap', 'airdrop', 'mining', 'fork']).notNullable();
    table.string('symbol', 20).notNullable();
    table.string('name', 100).nullable();
    table.decimal('amount', 30, 18).notNullable();
    table.decimal('price', 20, 8).nullable();
    table.decimal('total_value', 20, 8).nullable();
    table.decimal('fee', 20, 8).defaultTo(0);
    table.string('fee_currency', 10).defaultTo('USD');
    
    // Exchange/platform information
    table.string('exchange', 100).nullable();
    table.string('exchange_transaction_id', 255).nullable();
    table.string('wallet_address', 100).nullable();
    table.string('transaction_hash', 100).nullable();
    table.string('network', 50).nullable();
    table.integer('block_number').nullable();
    table.integer('confirmations').defaultTo(0);
    
    // Swap/trade specific fields
    table.string('from_symbol', 20).nullable();
    table.string('to_symbol', 20).nullable();
    table.decimal('from_amount', 30, 18).nullable();
    table.decimal('to_amount', 30, 18).nullable();
    table.decimal('exchange_rate', 20, 8).nullable();
    
    // Tax and accounting
    table.decimal('cost_basis', 20, 8).nullable();
    table.decimal('realized_pnl', 20, 8).nullable();
    table.string('tax_lot_method', 50).defaultTo('FIFO'); // FIFO, LIFO, HIFO, etc.
    table.boolean('is_taxable').defaultTo(true);
    
    // Status and metadata
    table.enum('status', ['pending', 'confirmed', 'failed', 'cancelled']).defaultTo('confirmed');
    table.text('notes').nullable();
    table.json('metadata').nullable();
    table.json('tags').nullable();
    table.timestamp('transaction_date').notNullable();
    table.timestamp('imported_at').nullable();
    table.string('import_source', 100).nullable(); // manual, csv, api, etc.
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['portfolio_id']);
    table.index(['type']);
    table.index(['symbol']);
    table.index(['exchange']);
    table.index(['status']);
    table.index(['transaction_date']);
    table.index(['created_at']);
    table.index(['is_deleted']);
    table.index(['user_id', 'transaction_date']);
    table.index(['portfolio_id', 'symbol']);
    table.index(['exchange_transaction_id']);
    table.index(['transaction_hash']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('transactions');
};
