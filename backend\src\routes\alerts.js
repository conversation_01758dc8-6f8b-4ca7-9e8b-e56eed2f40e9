const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');

const db = require('../config/database');
const logger = require('../config/logger');
const { catchAsync } = require('../middleware/errorHandler');
const PriceService = require('../services/price');

const router = express.Router();
const priceService = new PriceService();

// Validation middleware
const createAlertValidation = [
  body('name').isLength({ min: 1, max: 100 }).trim(),
  body('symbol').isLength({ min: 1, max: 20 }).trim().toUpperCase(),
  body('type').isIn(['price', 'percentage', 'volume', 'market_cap', 'portfolio_value', 'portfolio_percentage']),
  body('condition').isIn(['above', 'below', 'crosses_above', 'crosses_below', 'equals']),
  body('target_value').isFloat({ min: 0 }),
  body('base_currency').optional().isLength({ max: 10 }),
  body('notification_methods').isArray().custom((methods) => {
    const validMethods = ['email', 'push', 'sms', 'webhook'];
    return methods.every(method => validMethods.includes(method));
  }),
  body('webhook_url').optional().isURL(),
  body('is_recurring').optional().isBoolean(),
  body('cooldown_minutes').optional().isInt({ min: 1, max: 1440 }),
  body('max_triggers').optional().isInt({ min: 1 }),
  body('expires_at').optional().isISO8601(),
];

const updateAlertValidation = [
  body('name').optional().isLength({ min: 1, max: 100 }).trim(),
  body('target_value').optional().isFloat({ min: 0 }),
  body('notification_methods').optional().isArray(),
  body('webhook_url').optional().isURL(),
  body('is_recurring').optional().isBoolean(),
  body('cooldown_minutes').optional().isInt({ min: 1, max: 1440 }),
  body('max_triggers').optional().isInt({ min: 1 }),
  body('expires_at').optional().isISO8601(),
  body('is_active').optional().isBoolean(),
];

// Get all alerts for user
router.get('/', catchAsync(async (req, res) => {
  const { 
    page = 1, 
    limit = 50, 
    symbol, 
    type, 
    status = 'all',
    sort = 'created_at',
    order = 'desc'
  } = req.query;
  
  const offset = (page - 1) * limit;

  let query = db('alerts')
    .where({ user_id: req.user.id, is_deleted: false })
    .limit(limit)
    .offset(offset)
    .orderBy(sort, order);

  if (symbol) {
    query = query.where({ symbol: symbol.toUpperCase() });
  }

  if (type) {
    query = query.where({ type });
  }

  if (status === 'active') {
    query = query.where({ is_active: true });
  } else if (status === 'inactive') {
    query = query.where({ is_active: false });
  } else if (status === 'triggered') {
    query = query.where({ is_triggered: true });
  }

  const [alerts, totalCount] = await Promise.all([
    query,
    db('alerts')
      .where({ user_id: req.user.id, is_deleted: false })
      .count('id as count')
      .first()
  ]);

  // Parse JSON fields and add current prices
  const alertsWithPrices = await Promise.all(
    alerts.map(async (alert) => {
      const currentPrice = await priceService.getCurrentPrice(alert.symbol);
      
      return {
        ...alert,
        notification_methods: alert.notification_methods ? JSON.parse(alert.notification_methods) : [],
        conditions: alert.conditions ? JSON.parse(alert.conditions) : null,
        metadata: alert.metadata ? JSON.parse(alert.metadata) : null,
        tags: alert.tags ? JSON.parse(alert.tags) : null,
        current_price: currentPrice,
        distance_to_target: currentPrice ? Math.abs(currentPrice - alert.target_value) : null,
        distance_percentage: currentPrice ? ((Math.abs(currentPrice - alert.target_value) / currentPrice) * 100) : null
      };
    })
  );

  res.json({
    message: 'Alerts retrieved successfully',
    data: {
      alerts: alertsWithPrices,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(totalCount.count),
        pages: Math.ceil(totalCount.count / limit)
      }
    }
  });
}));

// Get single alert
router.get('/:id', catchAsync(async (req, res) => {
  const alert = await db('alerts')
    .where({ id: req.params.id, user_id: req.user.id, is_deleted: false })
    .first();

  if (!alert) {
    return res.status(404).json({
      error: 'Alert not found',
      message: 'Alert not found or access denied'
    });
  }

  // Get current price
  const currentPrice = await priceService.getCurrentPrice(alert.symbol);

  const alertWithPrice = {
    ...alert,
    notification_methods: alert.notification_methods ? JSON.parse(alert.notification_methods) : [],
    conditions: alert.conditions ? JSON.parse(alert.conditions) : null,
    metadata: alert.metadata ? JSON.parse(alert.metadata) : null,
    tags: alert.tags ? JSON.parse(alert.tags) : null,
    current_price: currentPrice,
    distance_to_target: currentPrice ? Math.abs(currentPrice - alert.target_value) : null,
    distance_percentage: currentPrice ? ((Math.abs(currentPrice - alert.target_value) / currentPrice) * 100) : null
  };

  res.json({
    message: 'Alert retrieved successfully',
    data: alertWithPrice
  });
}));

// Create new alert
router.post('/', createAlertValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const {
    name,
    description,
    symbol,
    type,
    condition,
    target_value,
    base_currency = 'USD',
    notification_methods,
    webhook_url,
    webhook_headers,
    is_recurring = false,
    cooldown_minutes = 60,
    max_triggers,
    expires_at,
    conditions,
    time_frame = '1m',
    notes,
    tags
  } = req.body;

  // Verify symbol exists and get current price
  const currentPrice = await priceService.getCurrentPrice(symbol);
  if (!currentPrice) {
    return res.status(400).json({
      error: 'Invalid symbol',
      message: 'Symbol not found or price data unavailable'
    });
  }

  const alertId = uuidv4();
  const alert = {
    id: alertId,
    user_id: req.user.id,
    name,
    description,
    symbol: symbol.toUpperCase(),
    type,
    condition,
    target_value,
    base_currency,
    notification_methods: JSON.stringify(notification_methods),
    webhook_url,
    webhook_headers: webhook_headers ? JSON.stringify(webhook_headers) : null,
    is_recurring,
    cooldown_minutes,
    max_triggers,
    expires_at: expires_at ? new Date(expires_at) : null,
    conditions: conditions ? JSON.stringify(conditions) : null,
    time_frame,
    current_value: currentPrice,
    notes,
    tags: tags ? JSON.stringify(tags) : null,
    metadata: JSON.stringify({
      created_price: currentPrice,
      created_at: new Date().toISOString()
    }),
    created_at: new Date(),
    updated_at: new Date()
  };

  await db('alerts').insert(alert);

  logger.business('Alert created', { 
    userId: req.user.id, 
    alertId, 
    symbol, 
    type, 
    condition, 
    target_value 
  });

  res.status(201).json({
    message: 'Alert created successfully',
    data: {
      ...alert,
      notification_methods: JSON.parse(alert.notification_methods),
      conditions: alert.conditions ? JSON.parse(alert.conditions) : null,
      metadata: JSON.parse(alert.metadata),
      tags: alert.tags ? JSON.parse(alert.tags) : null
    }
  });
}));

// Update alert
router.put('/:id', updateAlertValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const alertId = req.params.id;
  
  // Check if alert exists and belongs to user
  const existingAlert = await db('alerts')
    .where({ id: alertId, user_id: req.user.id, is_deleted: false })
    .first();

  if (!existingAlert) {
    return res.status(404).json({
      error: 'Alert not found',
      message: 'Alert not found or access denied'
    });
  }

  const updateData = {
    updated_at: new Date()
  };

  // Update allowed fields
  const allowedFields = [
    'name', 'target_value', 'notification_methods', 'webhook_url', 
    'webhook_headers', 'is_recurring', 'cooldown_minutes', 'max_triggers', 
    'expires_at', 'is_active', 'notes', 'tags'
  ];

  allowedFields.forEach(field => {
    if (req.body[field] !== undefined) {
      if (field === 'notification_methods' || field === 'webhook_headers' || field === 'tags') {
        updateData[field] = JSON.stringify(req.body[field]);
      } else if (field === 'expires_at' && req.body[field]) {
        updateData[field] = new Date(req.body[field]);
      } else {
        updateData[field] = req.body[field];
      }
    }
  });

  // Reset trigger status if reactivating
  if (req.body.is_active === true && existingAlert.is_triggered) {
    updateData.is_triggered = false;
    updateData.last_triggered = null;
  }

  await db('alerts')
    .where({ id: alertId })
    .update(updateData);

  logger.business('Alert updated', { userId: req.user.id, alertId });

  res.json({
    message: 'Alert updated successfully',
    data: updateData
  });
}));

// Delete alert
router.delete('/:id', catchAsync(async (req, res) => {
  const alertId = req.params.id;
  
  const result = await db('alerts')
    .where({ id: alertId, user_id: req.user.id, is_deleted: false })
    .update({
      is_deleted: true,
      deleted_at: new Date(),
      updated_at: new Date()
    });

  if (result === 0) {
    return res.status(404).json({
      error: 'Alert not found',
      message: 'Alert not found or access denied'
    });
  }

  logger.business('Alert deleted', { userId: req.user.id, alertId });

  res.json({
    message: 'Alert deleted successfully'
  });
}));

// Test alert (trigger manually for testing)
router.post('/:id/test', catchAsync(async (req, res) => {
  const alertId = req.params.id;
  
  const alert = await db('alerts')
    .where({ id: alertId, user_id: req.user.id, is_deleted: false })
    .first();

  if (!alert) {
    return res.status(404).json({
      error: 'Alert not found',
      message: 'Alert not found or access denied'
    });
  }

  // Get current price
  const currentPrice = await priceService.getCurrentPrice(alert.symbol);
  
  if (!currentPrice) {
    return res.status(400).json({
      error: 'Price unavailable',
      message: 'Unable to get current price for testing'
    });
  }

  // Simulate alert trigger (this would normally be done by the cron service)
  const NotificationService = require('../services/notification');
  const notificationService = new NotificationService();

  const notification = {
    user_id: alert.user_id,
    title: `Test Alert: ${alert.symbol}`,
    message: `This is a test of your alert "${alert.name}". Current price: ${currentPrice} ${alert.base_currency}`,
    type: 'price_alert',
    channels: JSON.parse(alert.notification_methods),
    related_entity_type: 'alert',
    related_entity_id: alert.id,
    metadata: {
      symbol: alert.symbol,
      condition: alert.condition,
      target_value: alert.target_value,
      current_price: currentPrice,
      alert_name: alert.name,
      is_test: true
    }
  };

  await notificationService.createNotification(notification);

  logger.business('Alert test triggered', { userId: req.user.id, alertId, currentPrice });

  res.json({
    message: 'Test alert sent successfully',
    data: {
      current_price: currentPrice,
      target_value: alert.target_value,
      condition: alert.condition
    }
  });
}));

// Get alert statistics
router.get('/stats/summary', catchAsync(async (req, res) => {
  const stats = await db('alerts')
    .where({ user_id: req.user.id, is_deleted: false })
    .select(
      db.raw('COUNT(*) as total_alerts'),
      db.raw('COUNT(CASE WHEN is_active = true THEN 1 END) as active_alerts'),
      db.raw('COUNT(CASE WHEN is_triggered = true THEN 1 END) as triggered_alerts'),
      db.raw('COUNT(CASE WHEN type = ? THEN 1 END) as price_alerts', ['price']),
      db.raw('COUNT(CASE WHEN type = ? THEN 1 END) as volume_alerts', ['volume']),
      db.raw('COUNT(CASE WHEN expires_at IS NOT NULL AND expires_at < NOW() THEN 1 END) as expired_alerts')
    )
    .first();

  // Get most watched symbols
  const topSymbols = await db('alerts')
    .where({ user_id: req.user.id, is_deleted: false, is_active: true })
    .select('symbol')
    .count('symbol as count')
    .groupBy('symbol')
    .orderBy('count', 'desc')
    .limit(10);

  // Get recent triggers
  const recentTriggers = await db('alerts')
    .where({ user_id: req.user.id, is_deleted: false, is_triggered: true })
    .whereNotNull('last_triggered')
    .select('id', 'name', 'symbol', 'condition', 'target_value', 'trigger_value', 'last_triggered')
    .orderBy('last_triggered', 'desc')
    .limit(5);

  res.json({
    message: 'Alert statistics retrieved successfully',
    data: {
      summary: {
        total: parseInt(stats.total_alerts) || 0,
        active: parseInt(stats.active_alerts) || 0,
        triggered: parseInt(stats.triggered_alerts) || 0,
        expired: parseInt(stats.expired_alerts) || 0,
        by_type: {
          price: parseInt(stats.price_alerts) || 0,
          volume: parseInt(stats.volume_alerts) || 0
        }
      },
      top_symbols: topSymbols,
      recent_triggers: recentTriggers
    }
  });
}));

// Bulk operations
router.post('/bulk/activate', catchAsync(async (req, res) => {
  const { alert_ids } = req.body;
  
  if (!Array.isArray(alert_ids) || alert_ids.length === 0) {
    return res.status(400).json({
      error: 'Invalid input',
      message: 'alert_ids must be a non-empty array'
    });
  }

  const result = await db('alerts')
    .whereIn('id', alert_ids)
    .where({ user_id: req.user.id, is_deleted: false })
    .update({
      is_active: true,
      is_triggered: false,
      updated_at: new Date()
    });

  logger.business('Bulk alert activation', { userId: req.user.id, count: result });

  res.json({
    message: `${result} alerts activated successfully`,
    data: { activated_count: result }
  });
}));

router.post('/bulk/deactivate', catchAsync(async (req, res) => {
  const { alert_ids } = req.body;
  
  if (!Array.isArray(alert_ids) || alert_ids.length === 0) {
    return res.status(400).json({
      error: 'Invalid input',
      message: 'alert_ids must be a non-empty array'
    });
  }

  const result = await db('alerts')
    .whereIn('id', alert_ids)
    .where({ user_id: req.user.id, is_deleted: false })
    .update({
      is_active: false,
      updated_at: new Date()
    });

  logger.business('Bulk alert deactivation', { userId: req.user.id, count: result });

  res.json({
    message: `${result} alerts deactivated successfully`,
    data: { deactivated_count: result }
  });
}));

router.delete('/bulk/delete', catchAsync(async (req, res) => {
  const { alert_ids } = req.body;
  
  if (!Array.isArray(alert_ids) || alert_ids.length === 0) {
    return res.status(400).json({
      error: 'Invalid input',
      message: 'alert_ids must be a non-empty array'
    });
  }

  const result = await db('alerts')
    .whereIn('id', alert_ids)
    .where({ user_id: req.user.id, is_deleted: false })
    .update({
      is_deleted: true,
      deleted_at: new Date(),
      updated_at: new Date()
    });

  logger.business('Bulk alert deletion', { userId: req.user.id, count: result });

  res.json({
    message: `${result} alerts deleted successfully`,
    data: { deleted_count: result }
  });
}));

module.exports = router;
