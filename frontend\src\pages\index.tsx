import { NextPage } from 'next';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  CurrencyDollarIcon, 
  ShieldCheckIcon, 
  GlobeAltIcon,
  ArrowRightIcon,
  PlayIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

// Components
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import Stats from '@/components/landing/Stats';
import Testimonials from '@/components/landing/Testimonials';
import Pricing from '@/components/landing/Pricing';
import FAQ from '@/components/landing/FAQ';
import CTA from '@/components/landing/CTA';
import Footer from '@/components/landing/Footer';

// Hooks
import { useAuthStore } from '@/store/authStore';

// Types
interface LandingPageProps {}

const LandingPage: NextPage<LandingPageProps> = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    // Redirect authenticated users to dashboard
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  if (!mounted) {
    return null;
  }

  return (
    <>
      <Head>
        <title>CryptoSphere - Advanced Cryptocurrency Portfolio Management</title>
        <meta 
          name="description" 
          content="Manage your cryptocurrency portfolio with AI-powered analytics, real-time tracking, and advanced trading tools. Join thousands of crypto investors using CryptoSphere." 
        />
        <meta name="keywords" content="cryptocurrency, portfolio, bitcoin, ethereum, trading, analytics, DeFi, blockchain" />
        <meta property="og:title" content="CryptoSphere - Advanced Cryptocurrency Portfolio Management" />
        <meta property="og:description" content="Manage your cryptocurrency portfolio with AI-powered analytics, real-time tracking, and advanced trading tools." />
        <meta property="og:image" content="/images/og-image.png" />
        <meta property="og:url" content="https://cryptosphere.io" />
        <meta property="og:type" content="website" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="CryptoSphere - Advanced Cryptocurrency Portfolio Management" />
        <meta name="twitter:description" content="Manage your cryptocurrency portfolio with AI-powered analytics, real-time tracking, and advanced trading tools." />
        <meta name="twitter:image" content="/images/twitter-image.png" />
        <link rel="canonical" href="https://cryptosphere.io" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "CryptoSphere",
              "description": "Advanced Cryptocurrency Portfolio Management Platform",
              "url": "https://cryptosphere.io",
              "applicationCategory": "FinanceApplication",
              "operatingSystem": "Web",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              }
            })
          }}
        />
      </Head>

      <div className="min-h-screen bg-white dark:bg-dark-900">
        {/* Navigation */}
        <nav className="fixed top-0 w-full z-50 bg-white/80 dark:bg-dark-900/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              {/* Logo */}
              <div className="flex items-center">
                <Link href="/" className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg flex items-center justify-center">
                    <CurrencyDollarIcon className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-xl font-bold text-gray-900 dark:text-white">
                    CryptoSphere
                  </span>
                </Link>
              </div>

              {/* Navigation Links */}
              <div className="hidden md:flex items-center space-x-8">
                <Link href="#features" className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                  Features
                </Link>
                <Link href="#pricing" className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                  Pricing
                </Link>
                <Link href="#about" className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                  About
                </Link>
                <Link href="/docs" className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                  Docs
                </Link>
              </div>

              {/* Auth Buttons */}
              <div className="flex items-center space-x-4">
                <Link 
                  href="/auth/login"
                  className="text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                >
                  Sign In
                </Link>
                <Link 
                  href="/auth/register"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="pt-16">
          <Hero />
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-gray-50 dark:bg-dark-800">
          <Stats />
        </section>

        {/* Features Section */}
        <section id="features" className="py-20">
          <Features />
        </section>

        {/* Testimonials Section */}
        <section className="py-20 bg-gray-50 dark:bg-dark-800">
          <Testimonials />
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-20">
          <Pricing />
        </section>

        {/* FAQ Section */}
        <section className="py-20 bg-gray-50 dark:bg-dark-800">
          <FAQ />
        </section>

        {/* CTA Section */}
        <section className="py-20">
          <CTA />
        </section>

        {/* Footer */}
        <Footer />
      </div>
    </>
  );
};

// Set layout to landing
LandingPage.layout = 'landing';

export default LandingPage;
