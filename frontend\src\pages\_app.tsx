import type { AppProps } from 'next/app';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { ThemeProvider } from 'next-themes';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import NProgress from 'nprogress';

// Styles
import '@/styles/globals.css';
import '@/styles/nprogress.css';

// Components
import Layout from '@/components/Layout';
import AuthProvider from '@/components/providers/AuthProvider';
import WebSocketProvider from '@/components/providers/WebSocketProvider';
import ErrorBoundary from '@/components/ErrorBoundary';

// Hooks
import { useAuthStore } from '@/store/authStore';

// Utils
import { initializeAnalytics } from '@/utils/analytics';

// Types
interface CustomAppProps extends AppProps {
  Component: AppProps['Component'] & {
    requireAuth?: boolean;
    layout?: 'dashboard' | 'auth' | 'landing' | 'minimal';
  };
}

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

function MyApp({ Component, pageProps }: CustomAppProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { isAuthenticated, isLoading: authLoading } = useAuthStore();

  // Initialize analytics
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      initializeAnalytics();
    }
  }, []);

  // Handle route changes
  useEffect(() => {
    const handleStart = () => {
      setIsLoading(true);
      NProgress.start();
    };

    const handleStop = () => {
      setIsLoading(false);
      NProgress.done();
    };

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleStop);
    router.events.on('routeChangeError', handleStop);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleStop);
      router.events.off('routeChangeError', handleStop);
    };
  }, [router]);

  // Handle authentication redirects
  useEffect(() => {
    if (!authLoading && Component.requireAuth && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [authLoading, isAuthenticated, Component.requireAuth, router]);

  // Show loading screen while checking auth
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Don't render protected pages if not authenticated
  if (Component.requireAuth && !isAuthenticated) {
    return null;
  }

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <QueryClientProvider client={queryClient}>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem={true}
            disableTransitionOnChange={false}
          >
            <AuthProvider>
              <WebSocketProvider>
                <Layout layout={Component.layout || 'dashboard'}>
                  <Component {...pageProps} />
                </Layout>
                
                {/* Toast notifications */}
                <Toaster
                  position="top-right"
                  toastOptions={{
                    duration: 4000,
                    style: {
                      background: '#363636',
                      color: '#fff',
                    },
                    success: {
                      duration: 3000,
                      iconTheme: {
                        primary: '#22c55e',
                        secondary: '#fff',
                      },
                    },
                    error: {
                      duration: 5000,
                      iconTheme: {
                        primary: '#ef4444',
                        secondary: '#fff',
                      },
                    },
                  }}
                />
                
                {/* Development tools */}
                {process.env.NODE_ENV === 'development' && (
                  <ReactQueryDevtools initialIsOpen={false} />
                )}
              </WebSocketProvider>
            </AuthProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default MyApp;
