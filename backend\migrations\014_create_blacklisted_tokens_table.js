/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('blacklisted_tokens', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Token information
    table.string('token', 500).notNullable(); // JWT token ID (jti)
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Blacklist reason
    table.enum('reason', [
      'logout',
      'password_change',
      'security_breach',
      'admin_action',
      'token_expired',
      'suspicious_activity'
    ]).notNullable();
    
    // Token metadata
    table.timestamp('token_issued_at').nullable();
    table.timestamp('token_expires_at').nullable();
    table.string('user_agent', 500).nullable();
    table.string('ip_address', 45).nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['token']);
    table.index(['user_id']);
    table.index(['reason']);
    table.index(['created_at']);
    table.index(['token_expires_at']);
    
    // Unique constraint for token
    table.unique(['token'], 'unique_blacklisted_token');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('blacklisted_tokens');
};
