const { v4: uuidv4 } = require('uuid');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  // Deletes ALL existing entries
  await knex('market_data').del();
  
  // Sample market data for popular cryptocurrencies
  const marketData = [
    {
      id: uuidv4(),
      symbol: 'BTC',
      name: 'Bitcoin',
      coingecko_id: 'bitcoin',
      price_usd: 43250.75,
      price_btc: 1.0,
      price_eth: 18.45,
      market_cap: ************,
      fully_diluted_valuation: ************,
      volume_24h: 28500000000,
      volume_change_24h: 12.5,
      circulating_supply: 19600000,
      total_supply: 19600000,
      max_supply: 21000000,
      price_change_1h: 0.8,
      price_change_24h: 2.3,
      price_change_7d: -1.2,
      price_change_30d: 8.7,
      price_change_1y: 125.4,
      ath_price: 69045.00,
      ath_date: new Date('2021-11-10'),
      ath_change_percentage: -37.4,
      atl_price: 67.81,
      atl_date: new Date('2013-07-06'),
      atl_change_percentage: 63650.2,
      market_cap_rank: 1,
      volume_rank: 1,
      rsi_14: 58.2,
      ma_20: 42800.50,
      ma_50: 41200.25,
      ma_200: 38900.75,
      volatility_30d: 3.8,
      data_source: 'coingecko',
      fetched_at: new Date(),
      last_updated: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: uuidv4(),
      symbol: 'ETH',
      name: 'Ethereum',
      coingecko_id: 'ethereum',
      price_usd: 2345.60,
      price_btc: 0.0542,
      price_eth: 1.0,
      market_cap: ************,
      fully_diluted_valuation: ************,
      volume_24h: 15200000000,
      volume_change_24h: 8.3,
      circulating_supply: 120200000,
      total_supply: 120200000,
      max_supply: null,
      price_change_1h: 1.2,
      price_change_24h: 3.8,
      price_change_7d: 2.1,
      price_change_30d: 12.4,
      price_change_1y: 89.7,
      ath_price: 4878.26,
      ath_date: new Date('2021-11-10'),
      ath_change_percentage: -51.9,
      atl_price: 0.432979,
      atl_date: new Date('2015-10-20'),
      atl_change_percentage: 541650.8,
      market_cap_rank: 2,
      volume_rank: 2,
      rsi_14: 62.1,
      ma_20: 2280.30,
      ma_50: 2150.75,
      ma_200: 1980.50,
      volatility_30d: 4.2,
      github_stars: 18500,
      github_forks: 7200,
      github_commits_4w: 245,
      github_contributors: 850,
      data_source: 'coingecko',
      fetched_at: new Date(),
      last_updated: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: uuidv4(),
      symbol: 'BNB',
      name: 'BNB',
      coingecko_id: 'binancecoin',
      price_usd: 315.80,
      price_btc: 0.0073,
      price_eth: 0.1346,
      market_cap: 47200000000,
      fully_diluted_valuation: 47200000000,
      volume_24h: 1850000000,
      volume_change_24h: -5.2,
      circulating_supply: 149500000,
      total_supply: 149500000,
      max_supply: 200000000,
      price_change_1h: -0.3,
      price_change_24h: 1.8,
      price_change_7d: -2.5,
      price_change_30d: 15.2,
      price_change_1y: 45.8,
      ath_price: 686.31,
      ath_date: new Date('2021-05-10'),
      ath_change_percentage: -54.0,
      atl_price: 0.0398177,
      atl_date: new Date('2017-10-19'),
      atl_change_percentage: 793250.5,
      market_cap_rank: 4,
      volume_rank: 8,
      rsi_14: 55.8,
      ma_20: 308.45,
      ma_50: 295.20,
      ma_200: 275.80,
      volatility_30d: 3.5,
      data_source: 'coingecko',
      fetched_at: new Date(),
      last_updated: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: uuidv4(),
      symbol: 'ADA',
      name: 'Cardano',
      coingecko_id: 'cardano',
      price_usd: 0.485,
      price_btc: 0.0000112,
      price_eth: 0.000207,
      market_cap: 17200000000,
      fully_diluted_valuation: 21800000000,
      volume_24h: 425000000,
      volume_change_24h: 18.7,
      circulating_supply: 35500000000,
      total_supply: 35500000000,
      max_supply: 45000000000,
      price_change_1h: 0.5,
      price_change_24h: 4.2,
      price_change_7d: 8.9,
      price_change_30d: 22.1,
      price_change_1y: -15.3,
      ath_price: 3.09,
      ath_date: new Date('2021-09-02'),
      ath_change_percentage: -84.3,
      atl_price: 0.01925275,
      atl_date: new Date('2020-03-13'),
      atl_change_percentage: 2420.8,
      market_cap_rank: 8,
      volume_rank: 15,
      rsi_14: 68.5,
      ma_20: 0.462,
      ma_50: 0.438,
      ma_200: 0.395,
      volatility_30d: 5.2,
      github_stars: 3200,
      github_forks: 1850,
      github_commits_4w: 180,
      github_contributors: 320,
      data_source: 'coingecko',
      fetched_at: new Date(),
      last_updated: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: uuidv4(),
      symbol: 'SOL',
      name: 'Solana',
      coingecko_id: 'solana',
      price_usd: 98.75,
      price_btc: 0.002284,
      price_eth: 0.0421,
      market_cap: 42800000000,
      fully_diluted_valuation: 56200000000,
      volume_24h: 2850000000,
      volume_change_24h: 25.8,
      circulating_supply: 433500000,
      total_supply: 569200000,
      max_supply: null,
      price_change_1h: 2.1,
      price_change_24h: 6.8,
      price_change_7d: 12.5,
      price_change_30d: 35.2,
      price_change_1y: 285.7,
      ath_price: 259.96,
      ath_date: new Date('2021-11-06'),
      ath_change_percentage: -62.0,
      atl_price: 0.500801,
      atl_date: new Date('2020-05-11'),
      atl_change_percentage: 19620.5,
      market_cap_rank: 5,
      volume_rank: 4,
      rsi_14: 72.3,
      ma_20: 89.20,
      ma_50: 78.45,
      ma_200: 65.80,
      volatility_30d: 6.8,
      github_stars: 8500,
      github_forks: 2100,
      github_commits_4w: 420,
      github_contributors: 180,
      data_source: 'coingecko',
      fetched_at: new Date(),
      last_updated: new Date(),
      created_at: new Date(),
      updated_at: new Date()
    }
  ];
  
  await knex('market_data').insert(marketData);
};
