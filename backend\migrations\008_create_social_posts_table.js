/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('social_posts', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to users
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Post content
    table.text('content').notNullable();
    table.enum('type', ['text', 'portfolio_share', 'trade_share', 'analysis', 'news_share', 'poll']).defaultTo('text');
    table.json('media_urls').nullable(); // Array of image/video URLs
    table.json('attachments').nullable(); // Portfolio snapshots, trade details, etc.
    
    // Post settings
    table.enum('visibility', ['public', 'followers', 'private']).defaultTo('public');
    table.boolean('allow_comments').defaultTo(true);
    table.boolean('allow_shares').defaultTo(true);
    
    // Engagement metrics
    table.integer('likes_count').defaultTo(0);
    table.integer('comments_count').defaultTo(0);
    table.integer('shares_count').defaultTo(0);
    table.integer('views_count').defaultTo(0);
    
    // Content moderation
    table.boolean('is_flagged').defaultTo(false);
    table.boolean('is_approved').defaultTo(true);
    table.text('moderation_notes').nullable();
    table.uuid('moderated_by').nullable();
    table.foreign('moderated_by').references('id').inTable('users');
    table.timestamp('moderated_at').nullable();
    
    // Hashtags and mentions
    table.json('hashtags').nullable();
    table.json('mentions').nullable(); // Array of user IDs
    table.json('symbols_mentioned').nullable(); // Array of crypto symbols
    
    // Metadata
    table.json('metadata').nullable();
    table.string('source_platform', 50).nullable(); // If imported from other platforms
    table.string('external_id', 255).nullable();
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['type']);
    table.index(['visibility']);
    table.index(['is_flagged']);
    table.index(['is_approved']);
    table.index(['created_at']);
    table.index(['is_deleted']);
    table.index(['user_id', 'visibility']);
    table.index(['type', 'visibility']);
    table.index(['created_at', 'visibility']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('social_posts');
};
