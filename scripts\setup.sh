#!/bin/bash

# CryptoSphere Setup Script
# This script sets up the development environment for CryptoSphere

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Node.js version
check_node_version() {
    if command_exists node; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        REQUIRED_VERSION="18.0.0"
        
        if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
            print_success "Node.js version $NODE_VERSION is compatible"
            return 0
        else
            print_error "Node.js version $NODE_VERSION is not compatible. Required: $REQUIRED_VERSION or higher"
            return 1
        fi
    else
        print_error "Node.js is not installed"
        return 1
    fi
}

# Function to check PostgreSQL
check_postgresql() {
    if command_exists psql; then
        print_success "PostgreSQL is installed"
        return 0
    else
        print_warning "PostgreSQL is not installed or not in PATH"
        return 1
    fi
}

# Function to check Redis
check_redis() {
    if command_exists redis-cli; then
        print_success "Redis is installed"
        return 0
    else
        print_warning "Redis is not installed or not in PATH"
        return 1
    fi
}

# Function to check Docker
check_docker() {
    if command_exists docker && command_exists docker-compose; then
        print_success "Docker and Docker Compose are installed"
        return 0
    else
        print_warning "Docker or Docker Compose is not installed"
        return 1
    fi
}

# Function to setup environment file
setup_env() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
            print_warning "Please edit .env file with your configuration"
        else
            print_error ".env.example file not found"
            return 1
        fi
    else
        print_warning ".env file already exists, skipping..."
    fi
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Backend dependencies
    if [ -d "backend" ]; then
        print_status "Installing backend dependencies..."
        cd backend
        npm ci
        cd ..
        print_success "Backend dependencies installed"
    fi
    
    # Frontend dependencies
    if [ -d "frontend" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend
        npm ci
        cd ..
        print_success "Frontend dependencies installed"
    fi
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    if [ -d "backend" ]; then
        cd backend
        
        # Check if database is accessible
        if npm run db:migrate > /dev/null 2>&1; then
            print_success "Database migrations completed"
            
            # Run seeds
            if npm run db:seed > /dev/null 2>&1; then
                print_success "Database seeding completed"
            else
                print_warning "Database seeding failed or no seeds available"
            fi
        else
            print_error "Database migration failed. Please check your database connection."
            print_status "Make sure PostgreSQL is running and .env is configured correctly"
        fi
        
        cd ..
    fi
}

# Function to start services with Docker
start_docker_services() {
    print_status "Starting services with Docker..."
    
    if docker-compose up -d postgres redis; then
        print_success "Database services started with Docker"
        
        # Wait for services to be ready
        print_status "Waiting for services to be ready..."
        sleep 10
        
        return 0
    else
        print_error "Failed to start Docker services"
        return 1
    fi
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    # Backend tests
    if [ -d "backend" ]; then
        print_status "Running backend tests..."
        cd backend
        if npm test > /dev/null 2>&1; then
            print_success "Backend tests passed"
        else
            print_warning "Backend tests failed or not configured"
        fi
        cd ..
    fi
    
    # Frontend tests
    if [ -d "frontend" ]; then
        print_status "Running frontend tests..."
        cd frontend
        if npm test > /dev/null 2>&1; then
            print_success "Frontend tests passed"
        else
            print_warning "Frontend tests failed or not configured"
        fi
        cd ..
    fi
}

# Function to display final instructions
show_final_instructions() {
    echo ""
    print_success "🎉 CryptoSphere setup completed!"
    echo ""
    echo "Next steps:"
    echo "1. Edit .env file with your configuration"
    echo "2. Start the development servers:"
    echo ""
    echo "   Option 1 - Using Docker (Recommended):"
    echo "   docker-compose up -d"
    echo ""
    echo "   Option 2 - Manual start:"
    echo "   # Terminal 1 - Backend"
    echo "   cd backend && npm run dev"
    echo ""
    echo "   # Terminal 2 - Frontend"
    echo "   cd frontend && npm run dev"
    echo ""
    echo "3. Open http://localhost:3000 in your browser"
    echo ""
    echo "For more information, see README.md"
    echo ""
}

# Main setup function
main() {
    echo ""
    echo "🚀 CryptoSphere Development Environment Setup"
    echo "=============================================="
    echo ""
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    DOCKER_AVAILABLE=false
    MANUAL_SETUP=false
    
    if ! check_node_version; then
        print_error "Please install Node.js 18+ and try again"
        exit 1
    fi
    
    if check_docker; then
        DOCKER_AVAILABLE=true
        print_status "Docker is available - will use Docker for database services"
    else
        print_status "Docker not available - checking for manual database setup"
        
        if check_postgresql && check_redis; then
            MANUAL_SETUP=true
            print_status "Manual database setup detected"
        else
            print_error "Either Docker or manual PostgreSQL/Redis setup is required"
            print_status "Please install Docker or PostgreSQL/Redis manually"
            exit 1
        fi
    fi
    
    # Setup environment
    setup_env
    
    # Start database services if using Docker
    if [ "$DOCKER_AVAILABLE" = true ]; then
        start_docker_services
    fi
    
    # Install dependencies
    install_dependencies
    
    # Setup database
    setup_database
    
    # Run tests (optional)
    if [ "$1" = "--with-tests" ]; then
        run_tests
    fi
    
    # Show final instructions
    show_final_instructions
}

# Parse command line arguments
case "$1" in
    --help|-h)
        echo "CryptoSphere Setup Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --with-tests    Run tests after setup"
        echo "  --help, -h      Show this help message"
        echo ""
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
