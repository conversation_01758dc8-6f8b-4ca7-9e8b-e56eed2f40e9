/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('watchlists', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to users
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Watchlist information
    table.string('name', 100).notNullable();
    table.text('description').nullable();
    table.boolean('is_public').defaultTo(false);
    table.boolean('is_default').defaultTo(false);
    table.string('color', 7).nullable(); // Hex color code
    table.string('icon', 50).nullable();
    
    // Settings
    table.json('sort_settings').nullable();
    table.json('display_settings').nullable();
    table.json('notification_settings').nullable();
    
    // Metadata
    table.json('metadata').nullable();
    table.json('tags').nullable();
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['is_public']);
    table.index(['is_default']);
    table.index(['created_at']);
    table.index(['is_deleted']);
    table.index(['user_id', 'is_deleted']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('watchlists');
};
