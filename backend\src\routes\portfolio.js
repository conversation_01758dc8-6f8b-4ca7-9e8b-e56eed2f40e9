const express = require('express');
const { body, validationResult, param } = require('express-validator');
const { v4: uuidv4 } = require('uuid');

const db = require('../config/database');
const logger = require('../config/logger');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { apiRateLimit } = require('../middleware/auth');
const PortfolioService = require('../services/portfolio');
const ExchangeService = require('../services/exchange');
const PriceService = require('../services/price');

const router = express.Router();
const portfolioService = new PortfolioService();
const exchangeService = new ExchangeService();
const priceService = new PriceService();

// Apply rate limiting
router.use(apiRateLimit);

// Validation rules
const createPortfolioValidation = [
  body('name').isLength({ min: 1, max: 100 }).withMessage('Portfolio name is required and must be less than 100 characters'),
  body('description').optional().isLength({ max: 500 }).withMessage('Description must be less than 500 characters'),
  body('isPublic').optional().isBoolean().withMessage('isPublic must be a boolean'),
  body('currency').optional().isIn(['USD', 'EUR', 'BTC', 'ETH']).withMessage('Invalid base currency')
];

const addHoldingValidation = [
  body('symbol').notEmpty().withMessage('Symbol is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
  body('averagePrice').optional().isFloat({ min: 0 }).withMessage('Average price must be a positive number'),
  body('exchange').optional().isString().withMessage('Exchange must be a string'),
  body('notes').optional().isLength({ max: 1000 }).withMessage('Notes must be less than 1000 characters')
];

const transactionValidation = [
  body('type').isIn(['buy', 'sell', 'transfer_in', 'transfer_out', 'stake', 'unstake', 'reward']).withMessage('Invalid transaction type'),
  body('symbol').notEmpty().withMessage('Symbol is required'),
  body('amount').isFloat({ min: 0 }).withMessage('Amount must be a positive number'),
  body('price').optional().isFloat({ min: 0 }).withMessage('Price must be a positive number'),
  body('fee').optional().isFloat({ min: 0 }).withMessage('Fee must be a positive number'),
  body('exchange').optional().isString().withMessage('Exchange must be a string'),
  body('transactionHash').optional().isString().withMessage('Transaction hash must be a string'),
  body('notes').optional().isLength({ max: 1000 }).withMessage('Notes must be less than 1000 characters')
];

// Get all portfolios for user
router.get('/', catchAsync(async (req, res) => {
  const portfolios = await portfolioService.getUserPortfolios(req.user.id);
  
  res.json({
    message: 'Portfolios retrieved successfully',
    data: portfolios
  });
}));

// Create new portfolio
router.post('/', createPortfolioValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const portfolioData = {
    ...req.body,
    userId: req.user.id
  };

  const portfolio = await portfolioService.createPortfolio(portfolioData);
  
  logger.business('Portfolio created', { 
    userId: req.user.id, 
    portfolioId: portfolio.id,
    portfolioName: portfolio.name 
  });

  res.status(201).json({
    message: 'Portfolio created successfully',
    data: portfolio
  });
}));

// Get specific portfolio
router.get('/:id', param('id').isUUID().withMessage('Invalid portfolio ID'), catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const portfolio = await portfolioService.getPortfolioById(req.params.id, req.user.id);
  
  if (!portfolio) {
    throw new AppError('Portfolio not found', 404);
  }

  res.json({
    message: 'Portfolio retrieved successfully',
    data: portfolio
  });
}));

// Update portfolio
router.put('/:id', [
  param('id').isUUID().withMessage('Invalid portfolio ID'),
  ...createPortfolioValidation
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const portfolio = await portfolioService.updatePortfolio(req.params.id, req.body, req.user.id);
  
  if (!portfolio) {
    throw new AppError('Portfolio not found', 404);
  }

  logger.business('Portfolio updated', { 
    userId: req.user.id, 
    portfolioId: portfolio.id 
  });

  res.json({
    message: 'Portfolio updated successfully',
    data: portfolio
  });
}));

// Delete portfolio
router.delete('/:id', param('id').isUUID().withMessage('Invalid portfolio ID'), catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const deleted = await portfolioService.deletePortfolio(req.params.id, req.user.id);
  
  if (!deleted) {
    throw new AppError('Portfolio not found', 404);
  }

  logger.business('Portfolio deleted', { 
    userId: req.user.id, 
    portfolioId: req.params.id 
  });

  res.json({
    message: 'Portfolio deleted successfully'
  });
}));

// Get portfolio holdings
router.get('/:id/holdings', param('id').isUUID().withMessage('Invalid portfolio ID'), catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const holdings = await portfolioService.getPortfolioHoldings(req.params.id, req.user.id);
  
  res.json({
    message: 'Holdings retrieved successfully',
    data: holdings
  });
}));

// Add holding to portfolio
router.post('/:id/holdings', [
  param('id').isUUID().withMessage('Invalid portfolio ID'),
  ...addHoldingValidation
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const holdingData = {
    ...req.body,
    portfolioId: req.params.id
  };

  const holding = await portfolioService.addHolding(holdingData, req.user.id);
  
  logger.business('Holding added', { 
    userId: req.user.id, 
    portfolioId: req.params.id,
    symbol: holding.symbol,
    amount: holding.amount
  });

  res.status(201).json({
    message: 'Holding added successfully',
    data: holding
  });
}));

// Update holding
router.put('/:portfolioId/holdings/:holdingId', [
  param('portfolioId').isUUID().withMessage('Invalid portfolio ID'),
  param('holdingId').isUUID().withMessage('Invalid holding ID'),
  ...addHoldingValidation
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const holding = await portfolioService.updateHolding(
    req.params.holdingId, 
    req.body, 
    req.user.id
  );
  
  if (!holding) {
    throw new AppError('Holding not found', 404);
  }

  logger.business('Holding updated', { 
    userId: req.user.id, 
    portfolioId: req.params.portfolioId,
    holdingId: req.params.holdingId
  });

  res.json({
    message: 'Holding updated successfully',
    data: holding
  });
}));

// Delete holding
router.delete('/:portfolioId/holdings/:holdingId', [
  param('portfolioId').isUUID().withMessage('Invalid portfolio ID'),
  param('holdingId').isUUID().withMessage('Invalid holding ID')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const deleted = await portfolioService.deleteHolding(req.params.holdingId, req.user.id);
  
  if (!deleted) {
    throw new AppError('Holding not found', 404);
  }

  logger.business('Holding deleted', { 
    userId: req.user.id, 
    portfolioId: req.params.portfolioId,
    holdingId: req.params.holdingId
  });

  res.json({
    message: 'Holding deleted successfully'
  });
}));

// Get portfolio transactions
router.get('/:id/transactions', [
  param('id').isUUID().withMessage('Invalid portfolio ID')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }

  const { page = 1, limit = 50, type, symbol, startDate, endDate } = req.query;
  
  const transactions = await portfolioService.getPortfolioTransactions(
    req.params.id, 
    req.user.id,
    { page: parseInt(page), limit: parseInt(limit), type, symbol, startDate, endDate }
  );
  
  res.json({
    message: 'Transactions retrieved successfully',
    data: transactions
  });
}));

module.exports = router;
