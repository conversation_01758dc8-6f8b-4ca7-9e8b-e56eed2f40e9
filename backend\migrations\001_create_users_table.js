/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Basic user information
    table.string('email', 255).notNullable().unique();
    table.string('username', 50).notNullable().unique();
    table.string('password_hash', 255).notNullable();
    table.string('first_name', 100).notNullable();
    table.string('last_name', 100).notNullable();
    
    // User status and role
    table.enum('role', ['user', 'admin', 'premium']).defaultTo('user');
    table.boolean('is_active').defaultTo(true);
    table.boolean('email_verified').defaultTo(false);
    
    // Email verification
    table.string('email_verification_token', 255).nullable();
    table.timestamp('email_verified_at').nullable();
    
    // Password reset
    table.string('password_reset_token', 255).nullable();
    table.timestamp('password_reset_expires').nullable();
    
    // Two-factor authentication
    table.boolean('two_factor_enabled').defaultTo(false);
    table.string('two_factor_secret', 255).nullable();
    table.boolean('two_factor_verified').defaultTo(false);
    table.json('backup_codes').nullable();
    
    // Security
    table.integer('failed_login_attempts').defaultTo(0);
    table.timestamp('locked_until').nullable();
    table.timestamp('last_login').nullable();
    table.timestamp('last_activity').nullable();
    
    // Profile information
    table.text('bio').nullable();
    table.string('avatar_url', 500).nullable();
    table.string('timezone', 50).defaultTo('UTC');
    table.string('language', 10).defaultTo('en');
    table.string('country', 2).nullable();
    
    // Preferences
    table.json('preferences').nullable();
    table.json('notification_settings').nullable();
    
    // Subscription and billing
    table.enum('subscription_tier', ['free', 'basic', 'premium', 'enterprise']).defaultTo('free');
    table.timestamp('subscription_expires_at').nullable();
    table.string('stripe_customer_id', 255).nullable();
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['email']);
    table.index(['username']);
    table.index(['is_active']);
    table.index(['email_verified']);
    table.index(['role']);
    table.index(['subscription_tier']);
    table.index(['created_at']);
    table.index(['is_deleted']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
