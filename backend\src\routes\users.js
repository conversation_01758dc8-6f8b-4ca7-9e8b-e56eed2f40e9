const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const multer = require('multer');
const sharp = require('sharp');
const path = require('path');
const fs = require('fs').promises;

const db = require('../config/database');
const logger = require('../config/logger');
const { authorize } = require('../middleware/auth');
const { catchAsync } = require('../middleware/errorHandler');

const router = express.Router();

// Rate limiting for sensitive operations
const sensitiveRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: 'Too many sensitive requests from this IP, please try again later.',
});

// Configure multer for avatar uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  },
});

// Validation middleware
const updateProfileValidation = [
  body('firstName').optional().isLength({ min: 1, max: 100 }).trim(),
  body('lastName').optional().isLength({ min: 1, max: 100 }).trim(),
  body('bio').optional().isLength({ max: 500 }).trim(),
  body('timezone').optional().isLength({ max: 50 }),
  body('language').optional().isIn(['en', 'es', 'fr', 'de', 'zh', 'ja']),
  body('country').optional().isLength({ min: 2, max: 2 }),
];

const changePasswordValidation = [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.newPassword) {
      throw new Error('Password confirmation does not match new password');
    }
    return true;
  }),
];

const updatePreferencesValidation = [
  body('theme').optional().isIn(['light', 'dark']),
  body('currency').optional().isIn(['USD', 'EUR', 'GBP', 'JPY', 'BTC', 'ETH']),
  body('notifications.email').optional().isBoolean(),
  body('notifications.push').optional().isBoolean(),
  body('notifications.sms').optional().isBoolean(),
];

// Get current user profile
router.get('/profile', catchAsync(async (req, res) => {
  const user = await db('users')
    .select([
      'id', 'email', 'username', 'first_name', 'last_name', 'bio', 
      'avatar_url', 'timezone', 'language', 'country', 'role',
      'subscription_tier', 'preferences', 'notification_settings',
      'two_factor_enabled', 'email_verified', 'created_at', 'last_activity'
    ])
    .where({ id: req.user.id })
    .first();

  if (!user) {
    return res.status(404).json({
      error: 'User not found',
      message: 'User profile not found'
    });
  }

  // Parse JSON fields
  user.preferences = user.preferences ? JSON.parse(user.preferences) : {};
  user.notification_settings = user.notification_settings ? JSON.parse(user.notification_settings) : {};

  res.json({
    message: 'Profile retrieved successfully',
    data: user
  });
}));

// Update user profile
router.put('/profile', updateProfileValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const { firstName, lastName, bio, timezone, language, country } = req.body;

  const updateData = {
    updated_at: new Date()
  };

  if (firstName !== undefined) updateData.first_name = firstName;
  if (lastName !== undefined) updateData.last_name = lastName;
  if (bio !== undefined) updateData.bio = bio;
  if (timezone !== undefined) updateData.timezone = timezone;
  if (language !== undefined) updateData.language = language;
  if (country !== undefined) updateData.country = country;

  await db('users')
    .where({ id: req.user.id })
    .update(updateData);

  logger.business('User profile updated', { userId: req.user.id });

  res.json({
    message: 'Profile updated successfully',
    data: updateData
  });
}));

// Upload avatar
router.post('/avatar', upload.single('avatar'), catchAsync(async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      error: 'No file uploaded',
      message: 'Please select an image file to upload'
    });
  }

  try {
    // Process image with sharp
    const processedImage = await sharp(req.file.buffer)
      .resize(200, 200, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 90 })
      .toBuffer();

    // Generate filename
    const filename = `avatar_${req.user.id}_${Date.now()}.jpg`;
    const uploadDir = path.join(__dirname, '../../uploads/avatars');
    
    // Ensure upload directory exists
    await fs.mkdir(uploadDir, { recursive: true });
    
    const filepath = path.join(uploadDir, filename);
    await fs.writeFile(filepath, processedImage);

    // Update user avatar URL
    const avatarUrl = `/uploads/avatars/${filename}`;
    await db('users')
      .where({ id: req.user.id })
      .update({ 
        avatar_url: avatarUrl,
        updated_at: new Date()
      });

    logger.business('User avatar updated', { userId: req.user.id, filename });

    res.json({
      message: 'Avatar uploaded successfully',
      data: { avatar_url: avatarUrl }
    });
  } catch (error) {
    logger.errorWithContext(error, { userId: req.user.id, operation: 'uploadAvatar' });
    res.status(500).json({
      error: 'Upload failed',
      message: 'Failed to process and save avatar image'
    });
  }
}));

// Change password
router.put('/password', sensitiveRateLimit, changePasswordValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  const { currentPassword, newPassword } = req.body;

  // Get current user with password hash
  const user = await db('users')
    .select('password_hash')
    .where({ id: req.user.id })
    .first();

  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
  if (!isCurrentPasswordValid) {
    return res.status(400).json({
      error: 'Invalid password',
      message: 'Current password is incorrect'
    });
  }

  // Hash new password
  const saltRounds = 12;
  const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

  // Update password
  await db('users')
    .where({ id: req.user.id })
    .update({
      password_hash: newPasswordHash,
      updated_at: new Date()
    });

  // Invalidate all existing sessions by blacklisting current tokens
  // This would require additional implementation to track active tokens

  logger.security('Password changed', { userId: req.user.id });

  res.json({
    message: 'Password changed successfully',
    data: { message: 'Please log in again with your new password' }
  });
}));

// Update preferences
router.put('/preferences', updatePreferencesValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      message: 'Please check your input data',
      details: errors.array()
    });
  }

  // Get current preferences
  const user = await db('users')
    .select('preferences')
    .where({ id: req.user.id })
    .first();

  const currentPreferences = user.preferences ? JSON.parse(user.preferences) : {};
  const updatedPreferences = { ...currentPreferences, ...req.body };

  await db('users')
    .where({ id: req.user.id })
    .update({
      preferences: JSON.stringify(updatedPreferences),
      updated_at: new Date()
    });

  logger.business('User preferences updated', { userId: req.user.id });

  res.json({
    message: 'Preferences updated successfully',
    data: updatedPreferences
  });
}));

// Update notification settings
router.put('/notifications', catchAsync(async (req, res) => {
  const allowedSettings = [
    'price_alerts', 'portfolio_updates', 'social_interactions', 
    'system_updates', 'marketing', 'email', 'push', 'sms'
  ];

  const settings = {};
  for (const [key, value] of Object.entries(req.body)) {
    if (allowedSettings.includes(key) && typeof value === 'boolean') {
      settings[key] = value;
    }
  }

  // Get current notification settings
  const user = await db('users')
    .select('notification_settings')
    .where({ id: req.user.id })
    .first();

  const currentSettings = user.notification_settings ? JSON.parse(user.notification_settings) : {};
  const updatedSettings = { ...currentSettings, ...settings };

  await db('users')
    .where({ id: req.user.id })
    .update({
      notification_settings: JSON.stringify(updatedSettings),
      updated_at: new Date()
    });

  logger.business('Notification settings updated', { userId: req.user.id });

  res.json({
    message: 'Notification settings updated successfully',
    data: updatedSettings
  });
}));

// Get user statistics
router.get('/stats', catchAsync(async (req, res) => {
  const [portfolioStats, transactionStats, alertStats] = await Promise.all([
    // Portfolio statistics
    db('portfolios')
      .where({ user_id: req.user.id, is_deleted: false })
      .select(
        db.raw('COUNT(*) as total_portfolios'),
        db.raw('SUM(current_value) as total_value'),
        db.raw('SUM(total_pnl) as total_pnl')
      )
      .first(),

    // Transaction statistics
    db('transactions')
      .where({ user_id: req.user.id, is_deleted: false })
      .select(
        db.raw('COUNT(*) as total_transactions'),
        db.raw('COUNT(CASE WHEN type = ? THEN 1 END) as buy_transactions', ['buy']),
        db.raw('COUNT(CASE WHEN type = ? THEN 1 END) as sell_transactions', ['sell'])
      )
      .first(),

    // Alert statistics
    db('alerts')
      .where({ user_id: req.user.id, is_deleted: false })
      .select(
        db.raw('COUNT(*) as total_alerts'),
        db.raw('COUNT(CASE WHEN is_active = true THEN 1 END) as active_alerts'),
        db.raw('COUNT(CASE WHEN is_triggered = true THEN 1 END) as triggered_alerts')
      )
      .first()
  ]);

  const stats = {
    portfolios: {
      total: parseInt(portfolioStats.total_portfolios) || 0,
      total_value: parseFloat(portfolioStats.total_value) || 0,
      total_pnl: parseFloat(portfolioStats.total_pnl) || 0
    },
    transactions: {
      total: parseInt(transactionStats.total_transactions) || 0,
      buy_count: parseInt(transactionStats.buy_transactions) || 0,
      sell_count: parseInt(transactionStats.sell_transactions) || 0
    },
    alerts: {
      total: parseInt(alertStats.total_alerts) || 0,
      active: parseInt(alertStats.active_alerts) || 0,
      triggered: parseInt(alertStats.triggered_alerts) || 0
    }
  };

  res.json({
    message: 'User statistics retrieved successfully',
    data: stats
  });
}));

// Delete account (soft delete)
router.delete('/account', sensitiveRateLimit, catchAsync(async (req, res) => {
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      error: 'Password required',
      message: 'Please provide your password to confirm account deletion'
    });
  }

  // Verify password
  const user = await db('users')
    .select('password_hash')
    .where({ id: req.user.id })
    .first();

  const isPasswordValid = await bcrypt.compare(password, user.password_hash);
  if (!isPasswordValid) {
    return res.status(400).json({
      error: 'Invalid password',
      message: 'Password is incorrect'
    });
  }

  // Soft delete user account
  await db('users')
    .where({ id: req.user.id })
    .update({
      is_deleted: true,
      is_active: false,
      deleted_at: new Date(),
      updated_at: new Date()
    });

  logger.security('User account deleted', { userId: req.user.id, email: req.user.email });

  res.json({
    message: 'Account deleted successfully',
    data: { message: 'Your account has been deactivated and will be permanently deleted after 30 days' }
  });
}));

// Admin routes (require admin role)
router.get('/admin/users', authorize(['admin']), catchAsync(async (req, res) => {
  const { page = 1, limit = 50, search, role, status } = req.query;
  const offset = (page - 1) * limit;

  let query = db('users')
    .select([
      'id', 'email', 'username', 'first_name', 'last_name', 'role',
      'subscription_tier', 'is_active', 'email_verified', 'created_at',
      'last_activity', 'is_deleted'
    ])
    .limit(limit)
    .offset(offset)
    .orderBy('created_at', 'desc');

  if (search) {
    query = query.where(function() {
      this.where('email', 'ilike', `%${search}%`)
        .orWhere('username', 'ilike', `%${search}%`)
        .orWhere('first_name', 'ilike', `%${search}%`)
        .orWhere('last_name', 'ilike', `%${search}%`);
    });
  }

  if (role) {
    query = query.where({ role });
  }

  if (status === 'active') {
    query = query.where({ is_active: true, is_deleted: false });
  } else if (status === 'inactive') {
    query = query.where({ is_active: false });
  } else if (status === 'deleted') {
    query = query.where({ is_deleted: true });
  }

  const [users, totalCount] = await Promise.all([
    query,
    db('users').count('id as count').first()
  ]);

  res.json({
    message: 'Users retrieved successfully',
    data: {
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: parseInt(totalCount.count),
        pages: Math.ceil(totalCount.count / limit)
      }
    }
  });
}));

module.exports = router;
