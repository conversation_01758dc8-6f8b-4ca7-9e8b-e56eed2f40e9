const { v4: uuidv4 } = require('uuid');
const db = require('../config/database');
const logger = require('../config/logger');
const EmailService = require('./email');

class NotificationService {
  constructor() {
    this.emailService = new EmailService();
  }

  async createNotification(notificationData) {
    try {
      const notification = {
        id: uuidv4(),
        user_id: notificationData.user_id,
        title: notificationData.title,
        message: notificationData.message,
        type: notificationData.type,
        priority: notificationData.priority || 'medium',
        channels: JSON.stringify(notificationData.channels || ['in_app']),
        related_entity_type: notificationData.related_entity_type || null,
        related_entity_id: notificationData.related_entity_id || null,
        action_url: notificationData.action_url || null,
        icon: notificationData.icon || null,
        image_url: notificationData.image_url || null,
        action_buttons: notificationData.action_buttons ? JSON.stringify(notificationData.action_buttons) : null,
        metadata: notificationData.metadata ? JSON.stringify(notificationData.metadata) : null,
        expires_at: notificationData.expires_at || null,
        created_at: new Date(),
        updated_at: new Date()
      };

      const [insertedNotification] = await db('notifications')
        .insert(notification)
        .returning('*');

      // Send notifications through various channels
      await this.deliverNotification(insertedNotification, notificationData.channels || ['in_app']);

      logger.info(`Notification created: ${notification.type} for user ${notification.user_id}`);
      return insertedNotification;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'createNotification', userId: notificationData.user_id });
      throw error;
    }
  }

  async deliverNotification(notification, channels) {
    try {
      const user = await db('users')
        .select('email', 'first_name', 'notification_settings')
        .where({ id: notification.user_id })
        .first();

      if (!user) {
        logger.warn(`User not found for notification: ${notification.user_id}`);
        return;
      }

      const userSettings = user.notification_settings ? JSON.parse(user.notification_settings) : {};

      // Check if user has enabled this type of notification
      if (!this.isNotificationTypeEnabled(notification.type, userSettings)) {
        logger.info(`Notification type ${notification.type} disabled for user ${notification.user_id}`);
        return;
      }

      // Deliver through each requested channel
      for (const channel of channels) {
        switch (channel) {
          case 'email':
            if (userSettings.email !== false) {
              await this.sendEmailNotification(notification, user);
            }
            break;
          case 'push':
            if (userSettings.push !== false) {
              await this.sendPushNotification(notification, user);
            }
            break;
          case 'sms':
            if (userSettings.sms !== false) {
              await this.sendSMSNotification(notification, user);
            }
            break;
          case 'in_app':
            // In-app notifications are stored in database by default
            break;
        }
      }
    } catch (error) {
      logger.errorWithContext(error, { notificationId: notification.id });
    }
  }

  isNotificationTypeEnabled(type, userSettings) {
    const typeMapping = {
      'price_alert': 'price_alerts',
      'portfolio_update': 'portfolio_updates',
      'trade_executed': 'portfolio_updates',
      'social_interaction': 'social_interactions',
      'system_update': 'system_updates',
      'security_alert': 'system_updates',
      'defi_position': 'portfolio_updates',
      'staking_reward': 'portfolio_updates',
      'news_update': 'system_updates',
      'market_update': 'system_updates'
    };

    const settingKey = typeMapping[type];
    return settingKey ? userSettings[settingKey] !== false : true;
  }

  async sendEmailNotification(notification, user) {
    try {
      let emailSent = false;

      // Use specific email templates for certain notification types
      switch (notification.type) {
        case 'price_alert':
          const metadata = notification.metadata ? JSON.parse(notification.metadata) : {};
          await this.emailService.sendPriceAlert(user.email, metadata);
          emailSent = true;
          break;
        
        default:
          // Generic notification email
          const html = `
            <h2>${notification.title}</h2>
            <p>${notification.message}</p>
            ${notification.action_url ? `<p><a href="${notification.action_url}">View Details</a></p>` : ''}
          `;
          await this.emailService.sendEmail(user.email, notification.title, html);
          emailSent = true;
          break;
      }

      if (emailSent) {
        await db('notifications')
          .where({ id: notification.id })
          .update({
            email_sent: true,
            email_sent_at: new Date()
          });
      }
    } catch (error) {
      logger.errorWithContext(error, { notificationId: notification.id, channel: 'email' });
    }
  }

  async sendPushNotification(notification, user) {
    try {
      // TODO: Implement push notification service (Firebase, OneSignal, etc.)
      // For now, just mark as sent
      await db('notifications')
        .where({ id: notification.id })
        .update({
          push_sent: true,
          push_sent_at: new Date()
        });

      logger.info(`Push notification sent: ${notification.id}`);
    } catch (error) {
      logger.errorWithContext(error, { notificationId: notification.id, channel: 'push' });
    }
  }

  async sendSMSNotification(notification, user) {
    try {
      // TODO: Implement SMS service (Twilio, AWS SNS, etc.)
      // For now, just mark as sent
      await db('notifications')
        .where({ id: notification.id })
        .update({
          sms_sent: true,
          sms_sent_at: new Date()
        });

      logger.info(`SMS notification sent: ${notification.id}`);
    } catch (error) {
      logger.errorWithContext(error, { notificationId: notification.id, channel: 'sms' });
    }
  }

  async getUserNotifications(userId, options = {}) {
    try {
      const {
        limit = 50,
        offset = 0,
        type = null,
        unreadOnly = false,
        includeArchived = false
      } = options;

      let query = db('notifications')
        .where({ user_id: userId, is_deleted: false })
        .orderBy('created_at', 'desc')
        .limit(limit)
        .offset(offset);

      if (type) {
        query = query.where({ type });
      }

      if (unreadOnly) {
        query = query.where({ is_read: false });
      }

      if (!includeArchived) {
        query = query.where({ is_archived: false });
      }

      // Filter out expired notifications
      query = query.where(function() {
        this.whereNull('expires_at').orWhere('expires_at', '>', new Date());
      });

      const notifications = await query;

      // Parse JSON fields
      return notifications.map(notification => ({
        ...notification,
        channels: notification.channels ? JSON.parse(notification.channels) : [],
        action_buttons: notification.action_buttons ? JSON.parse(notification.action_buttons) : null,
        metadata: notification.metadata ? JSON.parse(notification.metadata) : null
      }));
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getUserNotifications', userId });
      throw error;
    }
  }

  async markAsRead(notificationId, userId) {
    try {
      const result = await db('notifications')
        .where({ id: notificationId, user_id: userId })
        .update({
          is_read: true,
          read_at: new Date(),
          updated_at: new Date()
        });

      if (result === 0) {
        throw new Error('Notification not found or access denied');
      }

      logger.info(`Notification marked as read: ${notificationId}`);
      return true;
    } catch (error) {
      logger.errorWithContext(error, { notificationId, userId });
      throw error;
    }
  }

  async markAllAsRead(userId) {
    try {
      const result = await db('notifications')
        .where({ user_id: userId, is_read: false })
        .update({
          is_read: true,
          read_at: new Date(),
          updated_at: new Date()
        });

      logger.info(`Marked ${result} notifications as read for user ${userId}`);
      return result;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'markAllAsRead', userId });
      throw error;
    }
  }

  async archiveNotification(notificationId, userId) {
    try {
      const result = await db('notifications')
        .where({ id: notificationId, user_id: userId })
        .update({
          is_archived: true,
          archived_at: new Date(),
          updated_at: new Date()
        });

      if (result === 0) {
        throw new Error('Notification not found or access denied');
      }

      logger.info(`Notification archived: ${notificationId}`);
      return true;
    } catch (error) {
      logger.errorWithContext(error, { notificationId, userId });
      throw error;
    }
  }

  async deleteNotification(notificationId, userId) {
    try {
      const result = await db('notifications')
        .where({ id: notificationId, user_id: userId })
        .update({
          is_deleted: true,
          deleted_at: new Date(),
          updated_at: new Date()
        });

      if (result === 0) {
        throw new Error('Notification not found or access denied');
      }

      logger.info(`Notification deleted: ${notificationId}`);
      return true;
    } catch (error) {
      logger.errorWithContext(error, { notificationId, userId });
      throw error;
    }
  }

  async getUnreadCount(userId) {
    try {
      const result = await db('notifications')
        .where({ 
          user_id: userId, 
          is_read: false, 
          is_deleted: false,
          is_archived: false
        })
        .where(function() {
          this.whereNull('expires_at').orWhere('expires_at', '>', new Date());
        })
        .count('id as count')
        .first();

      return parseInt(result.count) || 0;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getUnreadCount', userId });
      return 0;
    }
  }

  async createSystemNotification(title, message, options = {}) {
    try {
      // Get all active users
      const users = await db('users')
        .select('id')
        .where({ is_active: true, email_verified: true });

      const notifications = users.map(user => ({
        user_id: user.id,
        title,
        message,
        type: 'system_update',
        priority: options.priority || 'medium',
        channels: options.channels || ['in_app'],
        ...options
      }));

      // Create notifications in batches
      const batchSize = 100;
      for (let i = 0; i < notifications.length; i += batchSize) {
        const batch = notifications.slice(i, i + batchSize);
        await Promise.all(batch.map(notification => this.createNotification(notification)));
      }

      logger.info(`System notification sent to ${users.length} users: ${title}`);
      return users.length;
    } catch (error) {
      logger.errorWithContext(error, { operation: 'createSystemNotification', title });
      throw error;
    }
  }

  async getNotificationStats(userId) {
    try {
      const stats = await db('notifications')
        .where({ user_id: userId, is_deleted: false })
        .select(
          db.raw('COUNT(*) as total'),
          db.raw('COUNT(CASE WHEN is_read = false THEN 1 END) as unread'),
          db.raw('COUNT(CASE WHEN is_archived = true THEN 1 END) as archived'),
          db.raw('COUNT(CASE WHEN type = ? THEN 1 END) as price_alerts', ['price_alert']),
          db.raw('COUNT(CASE WHEN type = ? THEN 1 END) as portfolio_updates', ['portfolio_update']),
          db.raw('COUNT(CASE WHEN type = ? THEN 1 END) as social_interactions', ['social_interaction'])
        )
        .first();

      return {
        total: parseInt(stats.total) || 0,
        unread: parseInt(stats.unread) || 0,
        archived: parseInt(stats.archived) || 0,
        by_type: {
          price_alerts: parseInt(stats.price_alerts) || 0,
          portfolio_updates: parseInt(stats.portfolio_updates) || 0,
          social_interactions: parseInt(stats.social_interactions) || 0
        }
      };
    } catch (error) {
      logger.errorWithContext(error, { operation: 'getNotificationStats', userId });
      return {
        total: 0,
        unread: 0,
        archived: 0,
        by_type: {
          price_alerts: 0,
          portfolio_updates: 0,
          social_interactions: 0
        }
      };
    }
  }
}

module.exports = NotificationService;
