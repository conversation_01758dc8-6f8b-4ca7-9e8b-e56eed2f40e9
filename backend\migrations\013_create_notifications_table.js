/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('notifications', function(table) {
    // Primary key
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    
    // Foreign key to users
    table.uuid('user_id').notNullable();
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    
    // Notification details
    table.string('title', 200).notNullable();
    table.text('message').notNullable();
    table.enum('type', [
      'price_alert', 
      'portfolio_update', 
      'trade_executed', 
      'social_interaction', 
      'system_update', 
      'security_alert',
      'defi_position',
      'staking_reward',
      'news_update',
      'market_update'
    ]).notNullable();
    
    // Priority and status
    table.enum('priority', ['low', 'medium', 'high', 'urgent']).defaultTo('medium');
    table.boolean('is_read').defaultTo(false);
    table.boolean('is_archived').defaultTo(false);
    table.timestamp('read_at').nullable();
    table.timestamp('archived_at').nullable();
    
    // Delivery channels
    table.json('channels').notNullable(); // ['in_app', 'email', 'push', 'sms']
    table.boolean('email_sent').defaultTo(false);
    table.boolean('push_sent').defaultTo(false);
    table.boolean('sms_sent').defaultTo(false);
    table.timestamp('email_sent_at').nullable();
    table.timestamp('push_sent_at').nullable();
    table.timestamp('sms_sent_at').nullable();
    
    // Related entities
    table.string('related_entity_type', 50).nullable(); // portfolio, alert, post, etc.
    table.uuid('related_entity_id').nullable();
    table.string('action_url', 500).nullable(); // Deep link or URL to related content
    
    // Rich content
    table.string('icon', 100).nullable();
    table.string('image_url', 500).nullable();
    table.json('action_buttons').nullable(); // Array of action buttons
    table.json('metadata').nullable();
    
    // Expiration
    table.timestamp('expires_at').nullable();
    
    // Soft delete
    table.boolean('is_deleted').defaultTo(false);
    table.timestamp('deleted_at').nullable();
    
    // Timestamps
    table.timestamps(true, true);
    
    // Indexes
    table.index(['user_id']);
    table.index(['type']);
    table.index(['priority']);
    table.index(['is_read']);
    table.index(['is_archived']);
    table.index(['created_at']);
    table.index(['expires_at']);
    table.index(['is_deleted']);
    table.index(['user_id', 'is_read']);
    table.index(['user_id', 'type']);
    table.index(['user_id', 'created_at']);
    table.index(['related_entity_type', 'related_entity_id']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('notifications');
};
